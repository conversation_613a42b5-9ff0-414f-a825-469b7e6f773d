import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  input,
} from '@angular/core';
import {
  FormPageContentComponent,
  HiddenSectionComponent,
  IMBFormGroupArrayComponent,
  IMBFormRadioButtonGroupComponent,
} from '@shared/ui';
import { FORM_METADATA } from '../../../../../generated/pacs008-form-metadata';
import {
  CashAccount38__1InputComponent,
  PartyIdentification135__1InputComponent,
  BranchAndFinancialInstitutionIdentification6__1InputComponent,
  FinancialInstitutionIdentification18__1InputComponent,
  CBPR_RestrictedFINXMax35TextInputComponent,
  BranchAndFinancialInstitutionIdentification6__2InputComponent,
  CBPR_RestrictedFINXMax140TextInputComponent,
} from '../../../components';
import { NonNullableFormBuilder, ReactiveFormsModule } from '@angular/forms';
import { createInstrForCdtrAgtGroup } from '../financial-institution-information.form';
import { Instruction3Code, instruction3Codes } from '@shared/types';
import { SelectOption } from '@helaba/iso20022-lib/util';

@Component({
  selector: 'app-financial-institution-information-form-content',
  imports: [
    HiddenSectionComponent,
    CashAccount38__1InputComponent,
    PartyIdentification135__1InputComponent,
    BranchAndFinancialInstitutionIdentification6__1InputComponent,
    FinancialInstitutionIdentification18__1InputComponent,
    CBPR_RestrictedFINXMax35TextInputComponent,
    BranchAndFinancialInstitutionIdentification6__2InputComponent,
    CBPR_RestrictedFINXMax140TextInputComponent,
    IMBFormGroupArrayComponent,
    ReactiveFormsModule,
    IMBFormRadioButtonGroupComponent,
  ],
  templateUrl:
    './financial-institution-information-form-content.component.html',
  styleUrl: './financial-institution-information-form-content.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinancialInstitutionInformationFormContentComponent extends FormPageContentComponent {
  private fb = inject(NonNullableFormBuilder);

  override pageKey = input.required<string>();
  isReadOnly = input.required<boolean>();

  override labels = computed(() => FORM_METADATA[this.pageKey()].labels);
  validationRules = computed(
    () => FORM_METADATA[this.pageKey()].validationRules
  );

  getInstrForCdtrAgtGroupFactory() {
    return () => createInstrForCdtrAgtGroup(this.fb);
  }

  instructionCodeOptions: SelectOption<Instruction3Code>[] =
    instruction3Codes.map((code) => ({
      key: code,
      label: code,
      value: code,
    }));
}
