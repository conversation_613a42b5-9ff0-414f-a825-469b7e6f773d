import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { ClearingSystemIdentification2Choice__1InputComponent } from '../clearing-system-identification-2-choice-1-input/clearing-system-identification-2-choice-1-input.component';
import { IMBFormTextInputComponent } from '@shared/ui';

@Component({
  selector: 'app-clearing-system-member-identification-2-1-input',
  imports: [
    ClearingSystemIdentification2Choice__1InputComponent,
    IMBFormTextInputComponent,
  ],
  templateUrl:
    './clearing-system-member-identification-2-1-input.component.html',
  styleUrl: './clearing-system-member-identification-2-1-input.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ClearingSystemMemberIdentification2__1InputComponent {
  clearingSystemIdCodeFieldName = input.required<string>();
  clearingSystemIdCodeLabel = input.required<string>();
  clearingSystemMemberIdFieldName = input.required<string>();
  clearingSystemMemberIdLabel = input.required<string>();
  isReadOnly = input.required<boolean>();
}
