import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { HiddenSectionComponent, IMBFormTextInputComponent } from '@shared/ui';
import {
  CBPR_RestrictedFINXMax140Text_ExtendedInputComponent,
  PostalAddress24__1InputComponent,
} from '../../../../components';

@Component({
  selector: 'app-related-remittance-information',
  imports: [
    CBPR_RestrictedFINXMax140Text_ExtendedInputComponent,
    HiddenSectionComponent,
    IMBFormTextInputComponent,
    PostalAddress24__1InputComponent,
  ],
  templateUrl: './related-remittance-information.component.html',
  styleUrl: './related-remittance-information.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RelatedRemittanceInformationComponent {
  isReadOnly = input.required<boolean>();
  labels = input.required<Record<string, string>>();
}
