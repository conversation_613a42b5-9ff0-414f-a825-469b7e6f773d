import {
  ChangeDetectionStrategy,
  Component,
  computed,
  input,
  Type,
} from '@angular/core';
import {
  ControlContainer,
  FormGroupDirective,
  ReactiveFormsModule,
} from '@angular/forms';
import { InputTextModule } from 'primeng/inputtext';
import { FormSingleInputComponent } from '@helaba/iso20022-lib/components';
import { FormFieldErrorsComponent } from '../../form-field-errors';
import { PlainFieldsetComponent } from '../../form-structures';
import { IMBFormValueComponent } from '../../imb-form-value';

@Component({
  selector: 'app-imb-form-text-input',
  imports: [
    ReactiveFormsModule,
    InputTextModule,
    FormSingleInputComponent,
    FormFieldErrorsComponent,
    IMBFormValueComponent,
  ],
  templateUrl: './imb-form-text-input.component.html',
  styleUrl: './imb-form-text-input.component.scss',
  viewProviders: [
    { provide: ControlContainer, useExisting: FormGroupDirective }, // When resolving 'ControlContainer', just use the parent form that was declared up the tree, required to use 'formControlName' within the component.
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class IMBFormTextInputComponent {
  label = input.required<string>();
  fieldName = input.required<string>();
  isReadOnly = input.required<boolean>();
  fieldPrefix = input<string>('');

  fieldsetComponent: Type<PlainFieldsetComponent> = PlainFieldsetComponent;
}
