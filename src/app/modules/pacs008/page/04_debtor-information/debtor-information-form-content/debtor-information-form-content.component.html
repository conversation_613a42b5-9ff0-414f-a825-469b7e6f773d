<app-cbpr-restricted-finx-max-140-text-extended-input
  fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Nm"
  [label]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Nm']"
  [isReadOnly]="isReadOnly()"
/>
<app-postal-address-24-1-input
  departmentFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-Dept"
  [departmentLabel]="
    labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-Dept']
  "
  subDepartmentFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-SubDept"
  [subDepartmentLabel]="
    labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-SubDept']
  "
  streetNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-StrtNm"
  [streetNameLabel]="
    labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-StrtNm']
  "
  buildingNumberFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-BldgNb"
  [buildingNumberLabel]="
    labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-BldgNb']
  "
  buildingNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-BldgNm"
  [buildingNameLabel]="
    labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-BldgNm']
  "
  floorFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-Flr"
  [floorLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-Flr']"
  postBoxFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-PstBx"
  [postBoxLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-PstBx']"
  roomFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-Room"
  [roomLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-Room']"
  postCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-PstCd"
  [postCodeLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-PstCd']"
  townNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-TwnNm"
  [townNameLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-TwnNm']"
  townLocationNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-TwnLctnNm"
  [townLocationNameLabel]="
    labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-TwnLctnNm']
  "
  districtNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-DstrctNm"
  [districtNameLabel]="
    labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-DstrctNm']
  "
  countrySubdivisionFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-CtrySubDvsn"
  [countrySubdivisionLabel]="
    labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-CtrySubDvsn']
  "
  countryFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-Ctry"
  [countryLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-Ctry']"
  addressLineFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-AdrLine"
  [addressLineLabel]="
    labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-AdrLine']
  "
  [isReadOnly]="isReadOnly()"
/>
<app-country-code-input
  fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-CtryOfRes"
  [label]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-CtryOfRes']"
  [isReadOnly]="isReadOnly()"
/>
<app-hidden-section
  header="Debtor Information"
  i18n-header
  [errorScopes]="['FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id']"
>
  <app-one-of-selector
    header="Choose between an organisation or an individual"
    i18n-header
    option1Label="Organisation"
    i18n-option1Label
    option2Label="Individual"
    i18n-option2Label
    [option1FieldNames]="[
      'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-AnyBIC',
      'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-LEI',
      'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr-Id',
      'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr-SchmeNm-Cd',
      'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr-Issr'
    ]"
    [option2FieldNames]="[
      'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt',
      'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth',
      'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth',
      'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth',
      'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-Id',
      'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-SchmeNm-Cd',
      'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-Issr'
    ]"
    [isReadOnly]="isReadOnly()"
  >
    <ng-container slot="option1">
      <app-any-bic-dec-2014-identifier-input
        fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-AnyBIC"
        [label]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-AnyBIC']"
        [isReadOnly]="isReadOnly()"
      />
      <app-lei-identifier-input
        fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-LEI"
        [label]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-LEI']"
        [isReadOnly]="isReadOnly()"
      />
      <app-cbpr-restricted-finx-max-35-text-input
        fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr-Id"
        [label]="
          labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr-Id']
        "
        [isReadOnly]="isReadOnly()"
      />
      <app-external-organisation-identification-1-code-input
        fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr-SchmeNm-Cd"
        [label]="
          labels()[
            'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr-SchmeNm-Cd'
          ]
        "
        [isReadOnly]="isReadOnly()"
      />
      <app-cbpr-restricted-finx-max-35-text-input
        fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr-Issr"
        [label]="
          labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr-Issr']
        "
        [isReadOnly]="isReadOnly()"
      />
    </ng-container>
    <ng-container slot="option2">
      <app-date-and-place-of-birth-1-1-input
        birthDateFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt"
        [birthDateLabel]="
          labels()[
            'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt'
          ]
        "
        provinceOfBirthFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth"
        [provinceOfBirthLabel]="
          labels()[
            'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth'
          ]
        "
        cityOfBirthFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth"
        [cityOfBirthLabel]="
          labels()[
            'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth'
          ]
        "
        countryOfBirthFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth"
        [countryOfBirthLabel]="
          labels()[
            'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth'
          ]
        "
        [isReadOnly]="isReadOnly()"
      />
      <!-- TODO: This must be an array -->
      <app-hidden-section
        header="Other Identification"
        i18n-header
        [errorScopes]="['FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr']"
      >
        <app-cbpr-restricted-finx-max-35-text-input
          fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-Id"
          [label]="
            labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-Id']
          "
          [isReadOnly]="isReadOnly()"
        />
        <app-external-person-identification-1-code-input
          fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-SchmeNm-Cd"
          [label]="
            labels()[
              'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-SchmeNm-Cd'
            ]
          "
          [isReadOnly]="isReadOnly()"
        />
        <app-cbpr-restricted-finx-max-35-text-input
          fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-Issr"
          [label]="
            labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-Issr']
          "
          [isReadOnly]="isReadOnly()"
        />
      </app-hidden-section>
    </ng-container>
  </app-one-of-selector>
</app-hidden-section>
<app-hidden-section
  header="Debtor Account"
  i18n-header
  [errorScopes]="['FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct']"
>
  <app-cash-account-38-1-input
    ibanFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-IBAN"
    [ibanLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-IBAN']"
    idFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Id"
    [idLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Id']"
    identificationSchemeNameCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Cd"
    [identificationSchemeNameCodeLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Cd']
    "
    proprietaryIdentificationSchemeNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Prtry"
    [proprietaryIdentificationSchemeNameLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Prtry']
    "
    identificationIssuerFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Issr"
    [identificationIssuerLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Issr']
    "
    typeCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Tp-Cd"
    [typeCodeLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Tp-Cd']"
    proprietaryTypeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Tp-Prtry"
    [proprietaryTypeLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Tp-Prtry']
    "
    currencyFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Ccy"
    [currencyLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Ccy']"
    nameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Nm"
    [nameLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Nm']"
    proxyTypeCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp-Cd"
    [proxyTypeCodeLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp-Cd']
    "
    proprietaryProxyTypeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp-Prtry"
    [proprietaryProxyTypeLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp-Prtry']
    "
    proxyIdFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Id"
    [proxyIdLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Id']"
    [isReadOnly]="isReadOnly()"
  />
</app-hidden-section>
<app-hidden-section
  header="Ultimate Debtor"
  i18n-header
  [errorScopes]="['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr']"
>
  <app-party-identification-135-1-input
    nameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Nm"
    [nameLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Nm']"
    departmentFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Dept"
    [departmentLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Dept']
    "
    subDepartmentFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-SubDept"
    [subDepartmentLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-SubDept']
    "
    streetNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-StrtNm"
    [streetNameLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-StrtNm']
    "
    buildingNumberFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-BldgNb"
    [buildingNumberLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-BldgNb']
    "
    buildingNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-BldgNm"
    [buildingNameLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-BldgNm']
    "
    floorFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Flr"
    [floorLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Flr']
    "
    postBoxFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-PstBx"
    [postBoxLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-PstBx']
    "
    roomFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Room"
    [roomLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Room']
    "
    postCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-PstCd"
    [postCodeLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-PstCd']
    "
    townNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-TwnNm"
    [townNameLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-TwnNm']
    "
    townLocationNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-TwnLctnNm"
    [townLocationNameLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-TwnLctnNm']
    "
    districtNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-DstrctNm"
    [districtNameLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-DstrctNm']
    "
    countrySubdivisionFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-CtrySubDvsn"
    [countrySubdivisionLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-CtrySubDvsn']
    "
    countryFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Ctry"
    [countryLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Ctry']
    "
    addressLineFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-AdrLine"
    [addressLineLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-AdrLine']
    "
    bicFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-AnyBIC"
    [bicLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-AnyBIC']
    "
    leiFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-LEI"
    [leiLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-LEI']
    "
    organisationIdFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-Id"
    [organisationIdLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-Id']
    "
    organisationIdSchemeCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-SchmeNm-Cd"
    [organisationIdSchemeCodeLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-SchmeNm-Cd'
      ]
    "
    proprietaryOrganisationIdSchemeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-SchmeNm-Prtry"
    [proprietaryOrganisationIdSchemeLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-SchmeNm-Prtry'
      ]
    "
    organisationIdIssuerFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-Issr"
    [organisationIdIssuerLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-Issr']
    "
    birthDateFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt"
    [birthDateLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt'
      ]
    "
    provinceOfBirthFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth"
    [provinceOfBirthLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth'
      ]
    "
    cityOfBirthFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth"
    [cityOfBirthLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth'
      ]
    "
    countryOfBirthFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth"
    [countryOfBirthLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth'
      ]
    "
    individualIdFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-Id"
    [individualIdLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-Id']
    "
    individualIdSchemeCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-SchmeNm-Cd"
    [individualIdSchemeCodeLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-SchmeNm-Cd'
      ]
    "
    proprietaryIndividualIdSchemeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-SchmeNm-Prtry"
    [proprietaryIndividualIdSchemeLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-SchmeNm-Prtry'
      ]
    "
    individualIdIssuerFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-Issr"
    [individualIdIssuerLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-Issr']
    "
    countryOfResidenceFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-CtryOfRes"
    [countryOfResidenceLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-CtryOfRes']
    "
    [isReadOnly]="isReadOnly()"
  />
</app-hidden-section>
