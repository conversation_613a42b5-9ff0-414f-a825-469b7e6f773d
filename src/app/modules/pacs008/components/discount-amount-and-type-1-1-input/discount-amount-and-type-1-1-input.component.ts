import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { IMBFormTextInputComponent, OneOfSelectorComponent } from '@shared/ui';
import { ActiveOrHistoricCurrencyAndAmountInputComponent } from '../active-or-historic-currency-and-amount-input';
import { CBPR_RestrictedFINXMax35Text_ExtendedInputComponent } from '../cbpr-restricted-finx-max-35-text-extended-input';

@Component({
  selector: 'app-discount-amount-and-type-1-1-input',
  imports: [
    IMBFormTextInputComponent,
    OneOfSelectorComponent,
    ActiveOrHistoricCurrencyAndAmountInputComponent,
    CBPR_RestrictedFINXMax35Text_ExtendedInputComponent,
  ],
  templateUrl: './discount-amount-and-type-1-1-input.component.html',
  styleUrl: './discount-amount-and-type-1-1-input.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DiscountAmountAndType1__1InputComponent {
  amountTypeCodeFieldName = input.required<string>();
  amountTypeCodeLabel = input.required<string>();
  amountTypeProprietaryFieldName = input.required<string>();
  amountTypeProprietaryLabel = input.required<string>();
  amountFieldName = input.required<string>();
  amountLabel = input.required<string>();
  currencyFieldName = input.required<string>();
  currencyLabel = input.required<string>();

  isReadOnly = input.required<boolean>();
}
