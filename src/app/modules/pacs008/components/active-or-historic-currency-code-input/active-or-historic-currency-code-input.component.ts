import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { IMBFormTextInputComponent } from '@shared/ui';

@Component({
  selector: 'app-active-or-historic-currency-code-input',
  imports: [IMBFormTextInputComponent],
  templateUrl: './active-or-historic-currency-code-input.component.html',
  styleUrl: './active-or-historic-currency-code-input.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ActiveOrHistoricCurrencyCodeInputComponent {
  fieldName = input.required<string>();
  label = input.required<string>();
  isReadOnly = input.required<boolean>();
}
