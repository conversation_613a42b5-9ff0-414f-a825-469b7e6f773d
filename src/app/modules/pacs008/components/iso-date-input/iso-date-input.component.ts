import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { IMBFormDateInputComponent } from '@shared/ui';

@Component({
  selector: 'app-iso-date-input',
  imports: [IMBFormDateInputComponent],
  templateUrl: './iso-date-input.component.html',
  styleUrl: './iso-date-input.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ISODateInputComponent {
  label = input.required<string>();
  fieldName = input.required<string>();
  isReadOnly = input.required<boolean>();
}
