import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { IMBFormTextInputComponent } from '@shared/ui';

@Component({
  selector: 'app-cbpr-amount-1-input',
  imports: [IMBFormTextInputComponent],
  templateUrl: './cbpr-amount-1-input.component.html',
  styleUrl: './cbpr-amount-1-input.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CBPR_Amount__1InputComponent {
  amountFieldName = input.required<string>();
  amountLabel = input.required<string>();
  currencyFieldName = input.required<string>();
  currencyLabel = input.required<string>();

  isReadOnly = input.required<boolean>();
}
