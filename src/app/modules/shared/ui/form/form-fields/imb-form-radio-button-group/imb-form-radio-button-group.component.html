<app-form-radio-button-group
  [label]="label()"
  [fieldName]="fieldName()"
  [isReadOnly]="isReadOnly()"
  [fieldTemplate]="fieldTemplate"
  [errorTemplate]="errorTemplate"
  [formValueFieldTemplate]="formValueFieldTemplate"
  [fieldPrefix]="fieldPrefix()"
  [options]="options()"
  [fieldsetComponent]="fieldsetComponent"
/>

<ng-template #fieldTemplate let-fieldId let-optionValue="optionValue">
  <!-- Cannot use p-radiobutton here because in nested form arrays PrimeNG seems to not respect the form hierarchy when generating the [name] attribute causing the selected option to be synced between array items. -->
  <input
    type="radio"
    [id]="fieldId"
    [value]="optionValue"
    [formControlName]="fieldName()"
  />
</ng-template>

<ng-template #errorTemplate let-fieldId>
  <app-form-field-errors [fieldName]="fieldName()" [fieldId]="fieldId" />
</ng-template>

<ng-template
  #formValueFieldTemplate
  let-fieldName
  let-formControl="formControl"
>
  <app-imb-form-value [fieldName]="fieldName" [formControl]="formControl" />
</ng-template>
