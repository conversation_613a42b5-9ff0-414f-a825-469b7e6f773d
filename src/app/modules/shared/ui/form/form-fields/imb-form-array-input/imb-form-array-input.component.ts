import { ChangeDetectionStrategy, Component, input, Type } from '@angular/core';
import {
  ControlContainer,
  FormGroupDirective,
  ReactiveFormsModule,
} from '@angular/forms';
import { InputTextModule } from 'primeng/inputtext';
import { ButtonModule } from 'primeng/button';
import { FormFieldErrorsComponent } from '../../form-field-errors';
import { FormArrayInputComponent } from '@helaba/iso20022-lib/components';
import { PlainFieldsetComponent } from '../../form-structures';
import { IMBFormValueComponent } from '../../imb-form-value';

@Component({
  selector: 'app-imb-form-array-input',
  imports: [
    ReactiveFormsModule,
    InputTextModule,
    FormFieldErrorsComponent,
    IMBFormValueComponent,
    FormArrayInputComponent,
    ButtonModule,
  ],
  templateUrl: './imb-form-array-input.component.html',
  styleUrl: './imb-form-array-input.component.scss',
  viewProviders: [
    { provide: ControlContainer, useExisting: FormGroupDirective }, // When resolving 'ControlContainer', just use the parent form that was declared up the tree, required to use 'formControlName' within the component.
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class IMBFormArrayInputComponent {
  label = input.required<string>();
  fieldName = input.required<string>();
  isReadOnly = input.required<boolean>();
  fieldPrefix = input<string>('');

  fieldsetComponent: Type<PlainFieldsetComponent> = PlainFieldsetComponent;
}
