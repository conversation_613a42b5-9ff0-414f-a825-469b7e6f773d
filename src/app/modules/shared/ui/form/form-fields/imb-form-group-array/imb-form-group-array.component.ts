import {
  ChangeDetectionStrategy,
  Component,
  input,
  TemplateRef,
  Type,
} from '@angular/core';
import {
  ControlContainer,
  FormGroup,
  FormGroupDirective,
  ReactiveFormsModule,
} from '@angular/forms';
import { InputTextModule } from 'primeng/inputtext';
import { ButtonModule } from 'primeng/button';
import { FormFieldErrorsComponent } from '../../form-field-errors';
import { FormGroupArrayComponent } from '@helaba/iso20022-lib/components';
import { Rule } from '@helaba/iso20022-lib/rules';
import { PlainFieldsetComponent } from '../../form-structures';
import { IMBFormValueComponent } from '../../imb-form-value';

@Component({
  selector: 'app-imb-form-group-array',
  imports: [
    ReactiveFormsModule,
    InputTextModule,
    FormFieldErrorsComponent,
    IMBFormValueComponent,
    FormGroupArrayComponent,
    ButtonModule,
  ],
  templateUrl: './imb-form-group-array.component.html',
  styleUrl: './imb-form-group-array.component.scss',
  viewProviders: [
    { provide: ControlContainer, useExisting: FormGroupDirective }, // When resolving 'ControlContainer', just use the parent form that was declared up the tree, required to use 'formControlName' within the component.
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class IMBFormGroupArrayComponent {
  label = input.required<string>();
  fieldName = input.required<string>();
  isReadOnly = input.required<boolean>();
  fieldPrefix = input<string>('');
  groupFactory = input.required<() => FormGroup>();
  itemTemplate = input.required<TemplateRef<unknown>>();
  validationRules = input.required<Rule<string, undefined>[]>();

  fieldsetComponent: Type<PlainFieldsetComponent> = PlainFieldsetComponent;
}
