import {
  ChangeDetectionStrategy,
  Component,
  computed,
  contentChild,
  inject,
  input,
  signal,
  TemplateRef,
} from '@angular/core';
import { NgTemplateOutlet } from '@angular/common';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { AccordionModule } from 'primeng/accordion';
import { ToastModule } from 'primeng/toast';
import { MessageService } from 'primeng/api';
import { InfoCardComponent } from '@shared/ui/info-card';
import { FormMergeService } from '@shared/services';
import { MessageControllerService } from '../../../../../../../api/services';
import { Result } from '../../../../../../../api/models';
import { HttpErrorResponse } from '@angular/common/http';
import { flattenArrays } from '@shared/utils';

@Component({
  selector: 'app-review-submit-page-content',
  imports: [
    ReactiveFormsModule,
    ButtonModule,
    CardModule,
    ToastModule,
    InfoCardComponent,
    AccordionModule,
    NgTemplateOutlet,
  ],
  templateUrl: './review-submit-page-content.component.html',
  styleUrl: './review-submit-page-content.component.scss',
  providers: [MessageService],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReviewSubmitPageContentComponent {
  formPageTitles = input.required<Record<string, string>>();

  formMergeService = inject(FormMergeService);
  messageService = inject(MessageService);

  constructor(private api: MessageControllerService) {}

  mergedFormData = this.formMergeService.mergedFormData;

  protected tabs = computed<
    { key: string; title: string; value: number; formGroup: FormGroup }[]
  >(() => {
    return Object.entries(this.mergedFormData()).map(
      ([pageKey, formGroup], index) => ({
        key: pageKey,
        title: this.formPageTitles()[pageKey] || pageKey,
        value: index,
        formGroup,
      })
    );
  });

  tabContentTemplate =
    contentChild<TemplateRef<{ $implicit: string; formGroup: FormGroup }>>(
      TemplateRef
    );

  isSubmitting = signal(false);

  async onSubmit(): Promise<void> {
    if (this.isSubmitting()) return;
    this.isSubmitting.set(true);
    const mergedFormData = this.mergedFormData();
    const formData = {
      ...mergedFormData['paymentBasics'].getRawValue(),
      ...mergedFormData['amountCurrency'].getRawValue(),
      ...mergedFormData['settlementInformation'].getRawValue(),
      ...mergedFormData['debtorInformation'].getRawValue(),
      ...mergedFormData['creditorInformation'].getRawValue(),
      ...mergedFormData['financialInstitutionInformation'].getRawValue(),
      ...mergedFormData['remittanceInformation'].getRawValue(),
      ...mergedFormData['regulatoryReporting'].getRawValue(),
    };

    const flattenedFormData = flattenArrays(formData);
    console.log('Submitting form data:', flattenedFormData);

    try {
      this.api
        .genXmlMessage({
          type: 'pacs_008_001_08',
          body: flattenedFormData,
        })
        .subscribe({
          next: (response: Blob) => {
            const contentType = response.type;

            if (contentType.includes('application/xml')) {
              const blobUrl = window.URL.createObjectURL(response);
              const a = document.createElement('a');
              a.href = blobUrl;
              a.download = 'message.xml';
              a.click();
              window.URL.revokeObjectURL(blobUrl);
            } else {
              console.error('Error generating XML message:', response);
              this.messageService.add({
                severity: 'error',
                summary: 'Error',
                detail: $localize`Failed to generate XML message.`,
              });
            }
          },
          error: (error: HttpErrorResponse) => {
            console.error('Error generating XML message:', error);
            const contentType = error.error.type;

            if (contentType.includes('application/json')) {
              const reader = new FileReader();
              reader.onload = () => {
                try {
                  const result: Result = JSON.parse(reader.result as string);
                  console.warn('Validation failed:', result);
                  this.messageService.add({
                    severity: 'error',
                    summary: 'Error',
                    detail: $localize`Failed to generate XML message.`,
                  });
                } catch (e) {
                  console.error('Failed to parse JSON:', e);
                }
              };
              reader.readAsText(error.error);
            } else {
              this.messageService.add({
                severity: 'error',
                summary: 'Error',
                detail: $localize`Failed to generate XML message.`,
              });
            }
          },
        });
    } catch (error) {
      console.error('Error during form submission:', error);
    } finally {
      this.isSubmitting.set(false);
    }
  }
}
