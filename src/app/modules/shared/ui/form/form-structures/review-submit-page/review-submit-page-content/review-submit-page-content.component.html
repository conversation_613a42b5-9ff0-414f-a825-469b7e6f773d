<app-info-card i18n>
  Please review all the information below before creating and downloading the
  pacs.008 message.
</app-info-card>

<div class="review-accordion-container">
  <p-accordion [multiple]="true">
    @for (tab of tabs(); track tab.key) {
    <p-accordion-panel [value]="tab.value">
      <p-accordion-header>{{ tab.title }}</p-accordion-header>
      <p-accordion-content>
        <ng-container
          *ngTemplateOutlet="
            tabContentTemplate()!;
            context: { $implicit: tab.key, formGroup: tab.formGroup }
          "
        ></ng-container>
      </p-accordion-content>
    </p-accordion-panel>
    }
  </p-accordion>
</div>

<div class="review-submit-button-container">
  <p-button
    label="Create pacs.008 message and download"
    i18n-label
    icon="pi pi-download"
    severity="success"
    [loading]="isSubmitting()"
    (onClick)="onSubmit()"
  />
</div>
<p-toast />
