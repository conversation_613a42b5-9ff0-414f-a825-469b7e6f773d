import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import {
  <PERSON><PERSON><PERSON>r,
  FormControl,
  FormGroupDirective,
  ReactiveFormsModule,
} from '@angular/forms';
import { InputTextModule } from 'primeng/inputtext';

@Component({
  selector: 'app-imb-form-value',
  imports: [ReactiveFormsModule, InputTextModule],
  templateUrl: './imb-form-value.component.html',
  styleUrl: './imb-form-value.component.scss',
  viewProviders: [
    { provide: ControlContainer, useExisting: FormGroupDirective }, // When resolving 'ControlContainer', just use the parent form that was declared up the tree, required to use 'formControlName' within the component.
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class IMBFormValueComponent {
  fieldName = input.required<string>();
  formControl = input.required<FormControl<string | null | undefined>>();
}
