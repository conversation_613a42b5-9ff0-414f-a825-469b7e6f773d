import {
  ChangeDetectionStrategy,
  Component,
  input,
  output,
  Type,
} from '@angular/core';
import {
  ControlContainer,
  FormGroupDirective,
  ReactiveFormsModule,
} from '@angular/forms';
import { FormFieldErrorsComponent } from '../../form-field-errors';
import { PlainFieldsetComponent } from '../../form-structures';
import { IMBFormValueComponent } from '../../imb-form-value';
import { DatePickerModule } from 'primeng/datepicker';
import { FormSingleInputComponent } from '@helaba/iso20022-lib/components';

@Component({
  selector: 'app-imb-form-date-input',
  imports: [
    ReactiveFormsModule,
    FormSingleInputComponent,
    DatePickerModule,
    FormFieldErrorsComponent,
    IMBFormValueComponent,
  ],
  templateUrl: './imb-form-date-input.component.html',
  styleUrl: './imb-form-date-input.component.scss',
  viewProviders: [
    { provide: ControlContainer, useExisting: FormGroupDirective }, // When resolving 'ControlContainer', just use the parent form that was declared up the tree, required to use 'formControlName' within the component.
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class IMBFormDateInputComponent {
  label = input.required<string>();
  fieldName = input.required<string>();
  isReadOnly = input.required<boolean>();
  fieldPrefix = input<string>('');

  showTime = input<boolean>(false);
  timeOnly = input<boolean>(false);

  fieldsetComponent: Type<PlainFieldsetComponent> = PlainFieldsetComponent;
}
