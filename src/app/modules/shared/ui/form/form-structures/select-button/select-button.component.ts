import {
  ChangeDetectionStrategy,
  Component,
  effect,
  input,
  model,
  output,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { SelectOption } from '@helaba/iso20022-lib/util';
import { SelectButtonModule } from 'primeng/selectbutton';

@Component({
  selector: 'app-select-button',
  imports: [SelectButtonModule, FormsModule],
  templateUrl: './select-button.component.html',
  styleUrl: './select-button.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SelectButtonComponent<T> {
  options = input.required<SelectOption<T>[]>();
  isReadOnly = input.required<boolean>();
  defaultValue = input<T | null>(null);
  onChange = output<T | null>();

  selectedOption = model<T | null>(null);

  constructor() {
    effect(() => {
      this.selectedOption.set(this.defaultValue());
    });
    effect(() => {
      this.onChange.emit(this.selectedOption());
    });
  }
}
