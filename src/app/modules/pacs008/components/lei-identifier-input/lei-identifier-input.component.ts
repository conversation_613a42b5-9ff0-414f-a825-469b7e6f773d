import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { IMBFormTextInputComponent } from '@shared/ui';

@Component({
  selector: 'app-lei-identifier-input',
  imports: [IMBFormTextInputComponent],
  templateUrl: './lei-identifier-input.component.html',
  styleUrl: './lei-identifier-input.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LEIIdentifierInputComponent {
  label = input.required<string>();
  fieldName = input.required<string>();
  isReadOnly = input.required<boolean>();
}
