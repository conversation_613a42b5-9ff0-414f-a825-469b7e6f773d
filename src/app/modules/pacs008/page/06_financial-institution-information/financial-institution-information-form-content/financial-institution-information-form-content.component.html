<app-hidden-section
  header="Initiating Party"
  i18n-header
  [errorScopes]="['FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty']"
>
  <app-party-identification-135-1-input
    nameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Nm"
    [nameLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Nm']"
    departmentFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-Dept"
    [departmentLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-Dept']
    "
    subDepartmentFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-SubDept"
    [subDepartmentLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-SubDept']
    "
    streetNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-StrtNm"
    [streetNameLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-StrtNm']
    "
    buildingNumberFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-BldgNb"
    [buildingNumberLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-BldgNb']
    "
    buildingNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-BldgNm"
    [buildingNameLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-BldgNm']
    "
    floorFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-Flr"
    [floorLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-Flr']
    "
    postBoxFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-PstBx"
    [postBoxLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-PstBx']
    "
    roomFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-Room"
    [roomLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-Room']
    "
    postCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-PstCd"
    [postCodeLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-PstCd']
    "
    townNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-TwnNm"
    [townNameLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-TwnNm']
    "
    townLocationNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-TwnLctnNm"
    [townLocationNameLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-TwnLctnNm']
    "
    districtNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-DstrctNm"
    [districtNameLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-DstrctNm']
    "
    countrySubdivisionFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-CtrySubDvsn"
    [countrySubdivisionLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-CtrySubDvsn']
    "
    countryFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-Ctry"
    [countryLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-Ctry']
    "
    addressLineFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-AdrLine"
    [addressLineLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-AdrLine']
    "
    countryOfResidenceFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-CtryOfRes"
    [countryOfResidenceLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-CtryOfRes']
    "
    bicFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-AnyBIC"
    [bicLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-AnyBIC']
    "
    leiFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-LEI"
    [leiLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-LEI']"
    organisationIdFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-Id"
    [organisationIdLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-Id']
    "
    organisationIdSchemeCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-SchmeNm-Cd"
    [organisationIdSchemeCodeLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-SchmeNm-Cd'
      ]
    "
    proprietaryOrganisationIdSchemeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-SchmeNm-Prtry"
    [proprietaryOrganisationIdSchemeLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-SchmeNm-Prtry'
      ]
    "
    organisationIdIssuerFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-Issr"
    [organisationIdIssuerLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-Issr']
    "
    birthDateFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-BirthDt"
    [birthDateLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-BirthDt'
      ]
    "
    provinceOfBirthFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth"
    [provinceOfBirthLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth'
      ]
    "
    cityOfBirthFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth"
    [cityOfBirthLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth'
      ]
    "
    countryOfBirthFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth"
    [countryOfBirthLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth'
      ]
    "
    individualIdFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-Id"
    [individualIdLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-Id']
    "
    individualIdSchemeCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-SchmeNm-Cd"
    [individualIdSchemeCodeLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-SchmeNm-Cd'
      ]
    "
    proprietaryIndividualIdSchemeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-SchmeNm-Prtry"
    [proprietaryIndividualIdSchemeLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-SchmeNm-Prtry'
      ]
    "
    individualIdIssuerFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-Issr"
    [individualIdIssuerLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-Issr']
    "
    [isReadOnly]="isReadOnly()"
  />
</app-hidden-section>
<app-hidden-section
  header="Debtor Agent"
  i18n-header
  [errorScopes]="['FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId']"
>
  <app-branch-and-financial-institution-identification-6-1-input
    bicfiFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-BICFI"
    [bicfiLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-BICFI']
    "
    clearingSystemIdCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"
    [clearingSystemIdCodeLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd'
      ]
    "
    clearingSystemMemberIdFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId"
    [clearingSystemMemberIdLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId'
      ]
    "
    leiIdentifierFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-LEI"
    [leiIdentifierLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-LEI']
    "
    nameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-Nm"
    [nameLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-Nm']
    "
    departmentFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Dept"
    [departmentLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Dept']
    "
    subDepartmentFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-SubDept"
    [subDepartmentLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-SubDept'
      ]
    "
    streetNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-StrtNm"
    [streetNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-StrtNm'
      ]
    "
    buildingNumberFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNb"
    [buildingNumberLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNb'
      ]
    "
    buildingNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNm"
    [buildingNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNm'
      ]
    "
    floorFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Flr"
    [floorLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Flr']
    "
    postBoxFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstBx"
    [postBoxLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstBx']
    "
    roomFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Room"
    [roomLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Room']
    "
    postCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstCd"
    [postCodeLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstCd']
    "
    townNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnNm"
    [townNameLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnNm']
    "
    townLocationNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnLctnNm"
    [townLocationNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnLctnNm'
      ]
    "
    districtNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-DstrctNm"
    [districtNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-DstrctNm'
      ]
    "
    countrySubdivisionFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-CtrySubDvsn"
    [countrySubdivisionLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-CtrySubDvsn'
      ]
    "
    countryFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Ctry"
    [countryLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Ctry']
    "
    addressLineFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-AdrLine"
    [addressLineLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-AdrLine'
      ]
    "
    [isReadOnly]="isReadOnly()"
  />
</app-hidden-section>
<app-hidden-section
  header="Debtor Agent Account"
  i18n-header
  [errorScopes]="['FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct']"
>
  <app-cash-account-38-1-input
    ibanFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-IBAN"
    [ibanLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-IBAN']"
    idFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Id"
    [idLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Id']"
    identificationSchemeNameCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-Cd"
    [identificationSchemeNameCodeLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-Cd']
    "
    proprietaryIdentificationSchemeNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-Prtry"
    [proprietaryIdentificationSchemeNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-Prtry'
      ]
    "
    identificationIssuerFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Issr"
    [identificationIssuerLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Issr']
    "
    typeCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Tp-Cd"
    [typeCodeLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Tp-Cd']
    "
    proprietaryTypeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Tp-Prtry"
    [proprietaryTypeLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Tp-Prtry']
    "
    currencyFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Ccy"
    [currencyLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Ccy']"
    nameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Nm"
    [nameLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Nm']"
    proxyTypeCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Cd"
    [proxyTypeCodeLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Cd']
    "
    proprietaryProxyTypeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Prtry"
    [proprietaryProxyTypeLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Prtry']
    "
    proxyIdFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Id"
    [proxyIdLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Id']
    "
    [isReadOnly]="isReadOnly()"
  />
</app-hidden-section>
<app-hidden-section
  header="Creditor Agent"
  i18n-header
  [errorScopes]="['FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt']"
>
  <app-financial-institution-identification-18-1-input
    bicfiFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-BICFI"
    [bicfiLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-BICFI']
    "
    clearingSystemIdCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"
    [clearingSystemIdCodeLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd'
      ]
    "
    clearingSystemMemberIdFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId"
    [clearingSystemMemberIdLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId'
      ]
    "
    leiIdentifierFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-LEI"
    [leiIdentifierLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-LEI']
    "
    nameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-Nm"
    [nameLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-Nm']
    "
    departmentFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Dept"
    [departmentLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Dept']
    "
    subDepartmentFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-SubDept"
    [subDepartmentLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-SubDept'
      ]
    "
    streetNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-StrtNm"
    [streetNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-StrtNm'
      ]
    "
    buildingNumberFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNb"
    [buildingNumberLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNb'
      ]
    "
    buildingNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNm"
    [buildingNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNm'
      ]
    "
    floorFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Flr"
    [floorLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Flr']
    "
    postBoxFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstBx"
    [postBoxLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstBx']
    "
    roomFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Room"
    [roomLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Room']
    "
    postCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstCd"
    [postCodeLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstCd']
    "
    townNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnNm"
    [townNameLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnNm']
    "
    townLocationNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnLctnNm"
    [townLocationNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnLctnNm'
      ]
    "
    districtNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-DstrctNm"
    [districtNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-DstrctNm'
      ]
    "
    countrySubdivisionFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-CtrySubDvsn"
    [countrySubdivisionLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-CtrySubDvsn'
      ]
    "
    countryFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Ctry"
    [countryLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Ctry']
    "
    addressLineFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-AdrLine"
    [addressLineLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-AdrLine'
      ]
    "
    [isReadOnly]="isReadOnly()"
  />
  <app-cbpr-restricted-finx-max-35-text-input
    fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-BrnchId-Id"
    [label]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-BrnchId-Id']"
    [isReadOnly]="isReadOnly()"
  />
</app-hidden-section>
<app-hidden-section
  header="Creditor Agent Account"
  i18n-header
  [errorScopes]="['FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct']"
  ><app-cash-account-38-1-input
    ibanFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-IBAN"
    [ibanLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-IBAN']"
    idFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Id"
    [idLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Id']"
    identificationSchemeNameCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-Cd"
    [identificationSchemeNameCodeLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-Cd']
    "
    proprietaryIdentificationSchemeNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-Prtry"
    [proprietaryIdentificationSchemeNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-Prtry'
      ]
    "
    identificationIssuerFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Issr"
    [identificationIssuerLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Issr']
    "
    typeCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Tp-Cd"
    [typeCodeLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Tp-Cd']
    "
    proprietaryTypeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Tp-Prtry"
    [proprietaryTypeLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Tp-Prtry']
    "
    currencyFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Ccy"
    [currencyLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Ccy']"
    nameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Nm"
    [nameLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Nm']"
    proxyTypeCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Cd"
    [proxyTypeCodeLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Cd']
    "
    proprietaryProxyTypeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Prtry"
    [proprietaryProxyTypeLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Prtry']
    "
    proxyIdFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Id"
    [proxyIdLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Id']
    "
    [isReadOnly]="isReadOnly()"
/></app-hidden-section>
<app-hidden-section
  header="Instructing Agent"
  i18n-header
  [errorScopes]="['FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId']"
>
  <app-branch-and-financial-institution-identification-6-2-input
    bicfiFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-BICFI"
    [bicfiLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-BICFI']
    "
    clearingSystemIdCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"
    [clearingSystemIdCodeLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd'
      ]
    "
    clearingSystemMemberIdFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId"
    [clearingSystemMemberIdLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId'
      ]
    "
    leiIdentifierFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-LEI"
    [leiIdentifierLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-LEI']
    "
    [isReadOnly]="isReadOnly()"
  />
</app-hidden-section>
<app-hidden-section
  header="Previous Instructing Agent 1"
  i18n-header
  [errorScopes]="['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId']"
>
  <app-branch-and-financial-institution-identification-6-1-input
    bicfiFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-BICFI"
    [bicfiLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-BICFI']
    "
    clearingSystemIdCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd"
    [clearingSystemIdCodeLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd'
      ]
    "
    clearingSystemMemberIdFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId"
    [clearingSystemMemberIdLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId'
      ]
    "
    leiIdentifierFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-LEI"
    [leiIdentifierLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-LEI']
    "
    nameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-Nm"
    [nameLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-Nm']
    "
    departmentFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Dept"
    [departmentLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Dept'
      ]
    "
    subDepartmentFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-SubDept"
    [subDepartmentLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-SubDept'
      ]
    "
    streetNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-StrtNm"
    [streetNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-StrtNm'
      ]
    "
    buildingNumberFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNb"
    [buildingNumberLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNb'
      ]
    "
    buildingNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNm"
    [buildingNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNm'
      ]
    "
    floorFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Flr"
    [floorLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Flr'
      ]
    "
    postBoxFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstBx"
    [postBoxLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstBx'
      ]
    "
    roomFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Room"
    [roomLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Room'
      ]
    "
    postCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstCd"
    [postCodeLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstCd'
      ]
    "
    townNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnNm"
    [townNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnNm'
      ]
    "
    townLocationNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnLctnNm"
    [townLocationNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnLctnNm'
      ]
    "
    districtNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-DstrctNm"
    [districtNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-DstrctNm'
      ]
    "
    countrySubdivisionFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-CtrySubDvsn"
    [countrySubdivisionLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-CtrySubDvsn'
      ]
    "
    countryFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Ctry"
    [countryLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Ctry'
      ]
    "
    addressLineFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-AdrLine"
    [addressLineLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-AdrLine'
      ]
    "
    [isReadOnly]="isReadOnly()"
  />
</app-hidden-section>
<app-hidden-section
  header="Previous Instructing Agent 1 Account"
  i18n-header
  [errorScopes]="['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct']"
  ><app-cash-account-38-1-input
    ibanFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-IBAN"
    [ibanLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-IBAN']
    "
    idFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Id"
    [idLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Id']
    "
    identificationSchemeNameCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-Cd"
    [identificationSchemeNameCodeLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-Cd'
      ]
    "
    proprietaryIdentificationSchemeNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-Prtry"
    [proprietaryIdentificationSchemeNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-Prtry'
      ]
    "
    identificationIssuerFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Issr"
    [identificationIssuerLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Issr']
    "
    typeCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-Cd"
    [typeCodeLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-Cd']
    "
    proprietaryTypeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-Prtry"
    [proprietaryTypeLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-Prtry']
    "
    currencyFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Ccy"
    [currencyLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Ccy']
    "
    nameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Nm"
    [nameLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Nm']"
    proxyTypeCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Cd"
    [proxyTypeCodeLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Cd']
    "
    proprietaryProxyTypeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Prtry"
    [proprietaryProxyTypeLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Prtry']
    "
    proxyIdFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Id"
    [proxyIdLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Id']
    "
    [isReadOnly]="isReadOnly()"
/></app-hidden-section>
<app-hidden-section
  header="Previous Instructing Agent 2"
  i18n-header
  [errorScopes]="['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId']"
  ><app-branch-and-financial-institution-identification-6-1-input
    bicfiFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-BICFI"
    [bicfiLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-BICFI']
    "
    clearingSystemIdCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd"
    [clearingSystemIdCodeLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd'
      ]
    "
    clearingSystemMemberIdFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId"
    [clearingSystemMemberIdLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId'
      ]
    "
    leiIdentifierFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-LEI"
    [leiIdentifierLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-LEI']
    "
    nameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-Nm"
    [nameLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-Nm']
    "
    departmentFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Dept"
    [departmentLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Dept'
      ]
    "
    subDepartmentFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-SubDept"
    [subDepartmentLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-SubDept'
      ]
    "
    streetNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-StrtNm"
    [streetNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-StrtNm'
      ]
    "
    buildingNumberFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNb"
    [buildingNumberLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNb'
      ]
    "
    buildingNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNm"
    [buildingNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNm'
      ]
    "
    floorFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Flr"
    [floorLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Flr'
      ]
    "
    postBoxFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstBx"
    [postBoxLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstBx'
      ]
    "
    roomFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Room"
    [roomLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Room'
      ]
    "
    postCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstCd"
    [postCodeLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstCd'
      ]
    "
    townNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnNm"
    [townNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnNm'
      ]
    "
    townLocationNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnLctnNm"
    [townLocationNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnLctnNm'
      ]
    "
    districtNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-DstrctNm"
    [districtNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-DstrctNm'
      ]
    "
    countrySubdivisionFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-CtrySubDvsn"
    [countrySubdivisionLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-CtrySubDvsn'
      ]
    "
    countryFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Ctry"
    [countryLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Ctry'
      ]
    "
    addressLineFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-AdrLine"
    [addressLineLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-AdrLine'
      ]
    "
    [isReadOnly]="isReadOnly()"
/></app-hidden-section>
<app-hidden-section
  header="Previous Instructing Agent 2 Account"
  i18n-header
  [errorScopes]="['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct']"
  ><app-cash-account-38-1-input
    ibanFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-IBAN"
    [ibanLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-IBAN']
    "
    idFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Id"
    [idLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Id']
    "
    identificationSchemeNameCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-Cd"
    [identificationSchemeNameCodeLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-Cd'
      ]
    "
    proprietaryIdentificationSchemeNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-Prtry"
    [proprietaryIdentificationSchemeNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-Prtry'
      ]
    "
    identificationIssuerFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Issr"
    [identificationIssuerLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Issr']
    "
    typeCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-Cd"
    [typeCodeLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-Cd']
    "
    proprietaryTypeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-Prtry"
    [proprietaryTypeLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-Prtry']
    "
    currencyFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Ccy"
    [currencyLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Ccy']
    "
    nameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Nm"
    [nameLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Nm']"
    proxyTypeCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Cd"
    [proxyTypeCodeLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Cd']
    "
    proprietaryProxyTypeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Prtry"
    [proprietaryProxyTypeLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Prtry']
    "
    proxyIdFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Id"
    [proxyIdLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Id']
    "
    [isReadOnly]="isReadOnly()"
/></app-hidden-section>
<app-hidden-section
  header="Previous Instructing Agent 3"
  i18n-header
  [errorScopes]="['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId']"
  ><app-branch-and-financial-institution-identification-6-1-input
    bicfiFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-BICFI"
    [bicfiLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-BICFI']
    "
    clearingSystemIdCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd"
    [clearingSystemIdCodeLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd'
      ]
    "
    clearingSystemMemberIdFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId"
    [clearingSystemMemberIdLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId'
      ]
    "
    leiIdentifierFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-LEI"
    [leiIdentifierLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-LEI']
    "
    nameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-Nm"
    [nameLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-Nm']
    "
    departmentFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Dept"
    [departmentLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Dept'
      ]
    "
    subDepartmentFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-SubDept"
    [subDepartmentLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-SubDept'
      ]
    "
    streetNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-StrtNm"
    [streetNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-StrtNm'
      ]
    "
    buildingNumberFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNb"
    [buildingNumberLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNb'
      ]
    "
    buildingNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNm"
    [buildingNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNm'
      ]
    "
    floorFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Flr"
    [floorLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Flr'
      ]
    "
    postBoxFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstBx"
    [postBoxLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstBx'
      ]
    "
    roomFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Room"
    [roomLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Room'
      ]
    "
    postCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstCd"
    [postCodeLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstCd'
      ]
    "
    townNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnNm"
    [townNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnNm'
      ]
    "
    townLocationNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnLctnNm"
    [townLocationNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnLctnNm'
      ]
    "
    districtNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-DstrctNm"
    [districtNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-DstrctNm'
      ]
    "
    countrySubdivisionFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-CtrySubDvsn"
    [countrySubdivisionLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-CtrySubDvsn'
      ]
    "
    countryFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Ctry"
    [countryLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Ctry'
      ]
    "
    addressLineFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-AdrLine"
    [addressLineLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-AdrLine'
      ]
    "
    [isReadOnly]="isReadOnly()"
/></app-hidden-section>
<app-hidden-section
  header="Previous Instructing Agent 3 Account"
  i18n-header
  [errorScopes]="['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct']"
  ><app-cash-account-38-1-input
    ibanFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-IBAN"
    [ibanLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-IBAN']
    "
    idFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Id"
    [idLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Id']
    "
    identificationSchemeNameCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-Cd"
    [identificationSchemeNameCodeLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-Cd'
      ]
    "
    proprietaryIdentificationSchemeNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-Prtry"
    [proprietaryIdentificationSchemeNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-Prtry'
      ]
    "
    identificationIssuerFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Issr"
    [identificationIssuerLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Issr']
    "
    typeCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-Cd"
    [typeCodeLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-Cd']
    "
    proprietaryTypeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-Prtry"
    [proprietaryTypeLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-Prtry']
    "
    currencyFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Ccy"
    [currencyLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Ccy']
    "
    nameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Nm"
    [nameLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Nm']"
    proxyTypeCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Cd"
    [proxyTypeCodeLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Cd']
    "
    proprietaryProxyTypeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Prtry"
    [proprietaryProxyTypeLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Prtry']
    "
    proxyIdFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Id"
    [proxyIdLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Id']
    "
    [isReadOnly]="isReadOnly()"
/></app-hidden-section>
<app-hidden-section
  header="Instructed Agent"
  i18n-header
  [errorScopes]="['FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId']"
>
  <app-branch-and-financial-institution-identification-6-2-input
    bicfiFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-BICFI"
    [bicfiLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-BICFI']
    "
    clearingSystemIdCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"
    [clearingSystemIdCodeLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd'
      ]
    "
    clearingSystemMemberIdFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId"
    [clearingSystemMemberIdLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId'
      ]
    "
    leiIdentifierFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-LEI"
    [leiIdentifierLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-LEI']
    "
    [isReadOnly]="isReadOnly()"
  />
</app-hidden-section>
<app-hidden-section
  header="Instructions"
  i18n-header
  [errorScopes]="[
    'FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt',
    'FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForNxtAgt'
  ]"
>
  <app-imb-form-group-array
    label="Instructions for Creditor Agent"
    i18n-label
    fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt"
    [groupFactory]="getInstrForCdtrAgtGroupFactory()"
    [itemTemplate]="instrForCdtrAgtTemplate"
    [isReadOnly]="isReadOnly()"
    [validationRules]="validationRules()"
  />
  <!-- TODO: Missing InstrForNxtAgt? -->
</app-hidden-section>
<app-hidden-section
  header="Intermediary Agents"
  i18n-header
  [errorScopes]="[
    'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId',
    'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct'
  ]"
  ><app-hidden-section
    header="Intermediary Agent 1"
    i18n-header
    [errorScopes]="['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId']"
  >
    <app-branch-and-financial-institution-identification-6-1-input
      bicfiFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-BICFI"
      [bicfiLabel]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-BICFI']
      "
      clearingSystemIdCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd"
      [clearingSystemIdCodeLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd'
        ]
      "
      clearingSystemMemberIdFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId"
      [clearingSystemMemberIdLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId'
        ]
      "
      leiIdentifierFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-LEI"
      [leiIdentifierLabel]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-LEI']
      "
      nameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-Nm"
      [nameLabel]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-Nm']
      "
      departmentFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Dept"
      [departmentLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Dept'
        ]
      "
      subDepartmentFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-SubDept"
      [subDepartmentLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-SubDept'
        ]
      "
      streetNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-StrtNm"
      [streetNameLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-StrtNm'
        ]
      "
      buildingNumberFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNb"
      [buildingNumberLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNb'
        ]
      "
      buildingNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNm"
      [buildingNameLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNm'
        ]
      "
      floorFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Flr"
      [floorLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Flr'
        ]
      "
      postBoxFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstBx"
      [postBoxLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstBx'
        ]
      "
      roomFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Room"
      [roomLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Room'
        ]
      "
      postCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstCd"
      [postCodeLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstCd'
        ]
      "
      townNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnNm"
      [townNameLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnNm'
        ]
      "
      townLocationNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnLctnNm"
      [townLocationNameLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnLctnNm'
        ]
      "
      districtNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-DstrctNm"
      [districtNameLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-DstrctNm'
        ]
      "
      countrySubdivisionFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-CtrySubDvsn"
      [countrySubdivisionLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-CtrySubDvsn'
        ]
      "
      countryFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Ctry"
      [countryLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Ctry'
        ]
      "
      addressLineFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-AdrLine"
      [addressLineLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-AdrLine'
        ]
      "
      [isReadOnly]="isReadOnly()"
    />
  </app-hidden-section>
  <app-hidden-section
    header="Intermediary Agent 1 Account"
    i18n-header
    [errorScopes]="['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct']"
  >
    <app-cash-account-38-1-input
      ibanFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-IBAN"
      [ibanLabel]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-IBAN']
      "
      idFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Id"
      [idLabel]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Id']
      "
      identificationSchemeNameCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-Cd"
      [identificationSchemeNameCodeLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-Cd'
        ]
      "
      proprietaryIdentificationSchemeNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-Prtry"
      [proprietaryIdentificationSchemeNameLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-Prtry'
        ]
      "
      identificationIssuerFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Issr"
      [identificationIssuerLabel]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Issr']
      "
      typeCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Tp-Cd"
      [typeCodeLabel]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Tp-Cd']
      "
      proprietaryTypeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Tp-Prtry"
      [proprietaryTypeLabel]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Tp-Prtry']
      "
      currencyFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Ccy"
      [currencyLabel]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Ccy']
      "
      nameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Nm"
      [nameLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Nm']"
      proxyTypeCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Cd"
      [proxyTypeCodeLabel]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Cd']
      "
      proprietaryProxyTypeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Prtry"
      [proprietaryProxyTypeLabel]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Prtry']
      "
      proxyIdFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Id"
      [proxyIdLabel]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Id']
      "
      [isReadOnly]="isReadOnly()"
    />
  </app-hidden-section>
  <app-hidden-section
    header="Intermediary Agent 2"
    i18n-header
    [errorScopes]="['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId']"
  >
    <app-branch-and-financial-institution-identification-6-1-input
      bicfiFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-BICFI"
      [bicfiLabel]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-BICFI']
      "
      clearingSystemIdCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd"
      [clearingSystemIdCodeLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd'
        ]
      "
      clearingSystemMemberIdFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId"
      [clearingSystemMemberIdLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId'
        ]
      "
      leiIdentifierFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-LEI"
      [leiIdentifierLabel]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-LEI']
      "
      nameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-Nm"
      [nameLabel]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-Nm']
      "
      departmentFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Dept"
      [departmentLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Dept'
        ]
      "
      subDepartmentFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-SubDept"
      [subDepartmentLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-SubDept'
        ]
      "
      streetNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-StrtNm"
      [streetNameLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-StrtNm'
        ]
      "
      buildingNumberFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNb"
      [buildingNumberLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNb'
        ]
      "
      buildingNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNm"
      [buildingNameLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNm'
        ]
      "
      floorFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Flr"
      [floorLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Flr'
        ]
      "
      postBoxFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstBx"
      [postBoxLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstBx'
        ]
      "
      roomFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Room"
      [roomLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Room'
        ]
      "
      postCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstCd"
      [postCodeLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstCd'
        ]
      "
      townNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnNm"
      [townNameLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnNm'
        ]
      "
      townLocationNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnLctnNm"
      [townLocationNameLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnLctnNm'
        ]
      "
      districtNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-DstrctNm"
      [districtNameLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-DstrctNm'
        ]
      "
      countrySubdivisionFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-CtrySubDvsn"
      [countrySubdivisionLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-CtrySubDvsn'
        ]
      "
      countryFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Ctry"
      [countryLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Ctry'
        ]
      "
      addressLineFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-AdrLine"
      [addressLineLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-AdrLine'
        ]
      "
      [isReadOnly]="isReadOnly()"
    />
  </app-hidden-section>
  <app-hidden-section
    header="Intermediary Agent 2 Account"
    i18n-header
    [errorScopes]="['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct']"
  >
    <app-cash-account-38-1-input
      ibanFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-IBAN"
      [ibanLabel]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-IBAN']
      "
      idFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Id"
      [idLabel]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Id']
      "
      identificationSchemeNameCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-Cd"
      [identificationSchemeNameCodeLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-Cd'
        ]
      "
      proprietaryIdentificationSchemeNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-Prtry"
      [proprietaryIdentificationSchemeNameLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-Prtry'
        ]
      "
      identificationIssuerFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Issr"
      [identificationIssuerLabel]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Issr']
      "
      typeCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Tp-Cd"
      [typeCodeLabel]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Tp-Cd']
      "
      proprietaryTypeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Tp-Prtry"
      [proprietaryTypeLabel]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Tp-Prtry']
      "
      currencyFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Ccy"
      [currencyLabel]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Ccy']
      "
      nameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Nm"
      [nameLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Nm']"
      proxyTypeCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Cd"
      [proxyTypeCodeLabel]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Cd']
      "
      proprietaryProxyTypeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Prtry"
      [proprietaryProxyTypeLabel]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Prtry']
      "
      proxyIdFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Id"
      [proxyIdLabel]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Id']
      "
      [isReadOnly]="isReadOnly()"
    />
  </app-hidden-section>
  <app-hidden-section
    header="Intermediary Agent 3"
    i18n-header
    [errorScopes]="['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId']"
  >
    <app-branch-and-financial-institution-identification-6-1-input
      bicfiFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-BICFI"
      [bicfiLabel]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-BICFI']
      "
      clearingSystemIdCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd"
      [clearingSystemIdCodeLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd'
        ]
      "
      clearingSystemMemberIdFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId"
      [clearingSystemMemberIdLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId'
        ]
      "
      leiIdentifierFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-LEI"
      [leiIdentifierLabel]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-LEI']
      "
      nameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-Nm"
      [nameLabel]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-Nm']
      "
      departmentFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Dept"
      [departmentLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Dept'
        ]
      "
      subDepartmentFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-SubDept"
      [subDepartmentLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-SubDept'
        ]
      "
      streetNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-StrtNm"
      [streetNameLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-StrtNm'
        ]
      "
      buildingNumberFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNb"
      [buildingNumberLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNb'
        ]
      "
      buildingNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNm"
      [buildingNameLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNm'
        ]
      "
      floorFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Flr"
      [floorLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Flr'
        ]
      "
      postBoxFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstBx"
      [postBoxLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstBx'
        ]
      "
      roomFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Room"
      [roomLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Room'
        ]
      "
      postCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstCd"
      [postCodeLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstCd'
        ]
      "
      townNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnNm"
      [townNameLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnNm'
        ]
      "
      townLocationNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnLctnNm"
      [townLocationNameLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnLctnNm'
        ]
      "
      districtNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-DstrctNm"
      [districtNameLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-DstrctNm'
        ]
      "
      countrySubdivisionFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-CtrySubDvsn"
      [countrySubdivisionLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-CtrySubDvsn'
        ]
      "
      countryFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Ctry"
      [countryLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Ctry'
        ]
      "
      addressLineFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-AdrLine"
      [addressLineLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-AdrLine'
        ]
      "
      [isReadOnly]="isReadOnly()"
    />
  </app-hidden-section>
  <app-hidden-section
    header="Intermediary Agent 3 Account"
    i18n-header
    [errorScopes]="['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct']"
  >
    <app-cash-account-38-1-input
      ibanFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-IBAN"
      [ibanLabel]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-IBAN']
      "
      idFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Id"
      [idLabel]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Id']
      "
      identificationSchemeNameCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-Cd"
      [identificationSchemeNameCodeLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-Cd'
        ]
      "
      proprietaryIdentificationSchemeNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-Prtry"
      [proprietaryIdentificationSchemeNameLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-Prtry'
        ]
      "
      identificationIssuerFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Issr"
      [identificationIssuerLabel]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Issr']
      "
      typeCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Tp-Cd"
      [typeCodeLabel]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Tp-Cd']
      "
      proprietaryTypeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Tp-Prtry"
      [proprietaryTypeLabel]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Tp-Prtry']
      "
      currencyFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Ccy"
      [currencyLabel]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Ccy']
      "
      nameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Nm"
      [nameLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Nm']"
      proxyTypeCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Cd"
      [proxyTypeCodeLabel]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Cd']
      "
      proprietaryProxyTypeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Prtry"
      [proprietaryProxyTypeLabel]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Prtry']
      "
      proxyIdFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Id"
      [proxyIdLabel]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Id']
      "
      [isReadOnly]="isReadOnly()"
    />
  </app-hidden-section>
</app-hidden-section>

<ng-template #instrForCdtrAgtTemplate let-control let-fieldPrefix="fieldPrefix">
  <div [formGroup]="control">
    <app-imb-form-radio-button-group
      fieldName="Cd"
      [label]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd']"
      [isReadOnly]="isReadOnly()"
      [fieldPrefix]="fieldPrefix"
      [options]="instructionCodeOptions"
    />
    <app-cbpr-restricted-finx-max-140-text-input
      fieldName="InstrInf"
      [label]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-InstrInf']
      "
      [fieldPrefix]="fieldPrefix"
      [isReadOnly]="isReadOnly()"
    />
  </div>
</ng-template>
