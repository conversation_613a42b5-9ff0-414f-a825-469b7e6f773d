<!-- Note: The only difference between PostalAddress24__1 and PostalAddress24__2 is that the AdrLine has 'maxItems: 2' instead of 'maxItems: 3' in PostalAddress24__2 and PostalAddress24__2 has a 'required' array containing 'TwnNm' and 'Ctry'. -->
<!-- StrtNm -->
<app-cbpr-restricted-finx-max-70-text-extended-input
  [fieldName]="streetNameFieldName()"
  [label]="streetNameLabel()"
  [isReadOnly]="isReadOnly()"
/>
<!-- PstCd -->
<app-cbpr-restricted-finx-max-16-text-extended-input
  [fieldName]="postCodeFieldName()"
  [label]="postCodeLabel()"
  [isReadOnly]="isReadOnly()"
/>
<!-- TwnNm -->
<app-cbpr-restricted-finx-max-35-text-extended-input
  [fieldName]="townNameFieldName()"
  [label]="townNameLabel()"
  [isReadOnly]="isReadOnly()"
/>
<!-- Ctry -->
<app-country-code-input
  [fieldName]="countryFieldName()"
  [label]="countryLabel()"
  [isReadOnly]="isReadOnly()"
/>
<!-- AdrLine -->
<!-- TODO: Allow child and use CBPR_RestrictedFINXMax70Text_Extended in here -->
<app-imb-form-array-input
  [fieldName]="addressLineFieldName()"
  [label]="addressLineLabel()"
  [isReadOnly]="isReadOnly()"
/>

<app-hidden-section
  header="Additional Fields"
  i18n-header
  [errorScopes]="[
    departmentFieldName(),
    subDepartmentFieldName(),
    buildingNumberFieldName(),
    buildingNameFieldName(),
    floorFieldName(),
    postBoxFieldName(),
    roomFieldName(),
    townLocationNameFieldName(),
    districtNameFieldName(),
    countrySubdivisionFieldName()
  ]"
>
  <!-- Dept -->
  <app-cbpr-restricted-finx-max-70-text-extended-input
    [fieldName]="departmentFieldName()"
    [label]="departmentLabel()"
    [isReadOnly]="isReadOnly()"
  />
  <!-- SubDept -->
  <app-cbpr-restricted-finx-max-70-text-extended-input
    [fieldName]="subDepartmentFieldName()"
    [label]="subDepartmentLabel()"
    [isReadOnly]="isReadOnly()"
  />
  <!-- BldgNb -->
  <app-cbpr-restricted-finx-max-16-text-extended-input
    [fieldName]="buildingNumberFieldName()"
    [label]="buildingNumberLabel()"
    [isReadOnly]="isReadOnly()"
  />
  <!-- BldgNm -->
  <app-cbpr-restricted-finx-max-35-text-extended-input
    [fieldName]="buildingNameFieldName()"
    [label]="buildingNameLabel()"
    [isReadOnly]="isReadOnly()"
  />
  <!-- Flr -->
  <app-cbpr-restricted-finx-max-70-text-extended-input
    [fieldName]="floorFieldName()"
    [label]="floorLabel()"
    [isReadOnly]="isReadOnly()"
  />
  <!-- PstBx -->
  <app-cbpr-restricted-finx-max-16-text-extended-input
    [fieldName]="postBoxFieldName()"
    [label]="postBoxLabel()"
    [isReadOnly]="isReadOnly()"
  />
  <!-- Room -->
  <app-cbpr-restricted-finx-max-70-text-extended-input
    [fieldName]="roomFieldName()"
    [label]="roomLabel()"
    [isReadOnly]="isReadOnly()"
  />
  <!-- TwnLctnNm -->
  <app-cbpr-restricted-finx-max-35-text-extended-input
    [fieldName]="townLocationNameFieldName()"
    [label]="townLocationNameLabel()"
    [isReadOnly]="isReadOnly()"
  />
  <!-- DstrctNm -->
  <app-cbpr-restricted-finx-max-35-text-extended-input
    [fieldName]="districtNameFieldName()"
    [label]="districtNameLabel()"
    [isReadOnly]="isReadOnly()"
  />
  <!-- CtrySubDvsn -->
  <app-cbpr-restricted-finx-max-35-text-extended-input
    [fieldName]="countrySubdivisionFieldName()"
    [label]="countrySubdivisionLabel()"
    [isReadOnly]="isReadOnly()"
  />
</app-hidden-section>
