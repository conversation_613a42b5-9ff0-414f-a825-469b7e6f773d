<app-info-card i18n>
  Configure basic information about the transaction and the payment, such as the
  service level and the category purpose.
</app-info-card>

<!-- @if (isEditMode()) {
<button (click)="isEditMode.set(false)">Click to view</button>
} @else {
<button (click)="isEditMode.set(true)">Click to edit</button>
} -->

<!-- @if (isEditMode()) {
<form [formGroup]="testFormGroup">
  <h2>Edit</h2>
  <app-form-text-input
    label="testField"
    [fieldNames]="['testField']"
    [isReadOnly]="false"
  />
  <app-form-group-array
    label="nestedFieldName"
    [fieldNames]="['nestedTestField']"
    [isReadOnly]="false"
    [groupFactory]="getTestGroupFactory()"
    [itemTemplate]="testTemplate"
    [labels]="testLabels"
  />
</form>
} @else {
<form [formGroup]="testFormGroup">
  <h2>View</h2>
  <app-form-text-input
    label="testField"
    [fieldNames]="['testField']"
    [isReadOnly]="true"
  />
  <app-form-group-array
    label="nestedFieldName"
    [fieldNames]="['nestedTestField']"
    [isReadOnly]="true"
    [groupFactory]="getTestGroupFactory()"
    [itemTemplate]="testTemplateReadonly"
    [labels]="testLabels"
  />
</form>

} -->

<form
  [formGroup]="formGroup"
  [formRules]="validationRules"
  [affectedFields]="affectedFields"
>
  <app-payment-basics-form-content [pageKey]="pageKey" [isReadOnly]="false" />
</form>
<app-form-navigation-buttons [steps]="formSteps" [formGroup]="formGroup" />

<!-- <ng-template #testTemplate let-control let-fieldPrefix="fieldPrefix">
  <div [formGroup]="control">
    <app-form-text-input
      [fieldNames]="['Test1']"
      label="Test 1"
      [fieldPrefix]="fieldPrefix"
      [isReadOnly]="false"
    />
    <app-form-text-input
      [fieldNames]="['Test2']"
      label="Test 2"
      [fieldPrefix]="fieldPrefix"
      [isReadOnly]="false"
    />
  </div>
</ng-template>

<ng-template #testTemplateReadonly let-control let-fieldPrefix="fieldPrefix">
  <div [formGroup]="control">
    <app-form-text-input
      [fieldNames]="['Test1']"
      label="Test 1"
      [fieldPrefix]="fieldPrefix"
      [isReadOnly]="true"
    />
    <app-form-text-input
      [fieldNames]="['Test2']"
      label="Test 2"
      [fieldPrefix]="fieldPrefix"
      [isReadOnly]="true"
    />
  </div>
</ng-template> -->
