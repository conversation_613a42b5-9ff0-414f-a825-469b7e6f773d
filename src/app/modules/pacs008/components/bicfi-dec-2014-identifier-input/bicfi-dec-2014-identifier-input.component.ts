import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { IMBFormTextInputComponent } from '@shared/ui';

@Component({
  selector: 'app-bicfi-dec-2014-identifier-input',
  imports: [IMBFormTextInputComponent],
  templateUrl: './bicfi-dec-2014-identifier-input.component.html',
  styleUrl: './bicfi-dec-2014-identifier-input.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BICFIDec2014IdentifierInputComponent {
  fieldName = input.required<string>();
  label = input.required<string>();
  isReadOnly = input.required<boolean>();
}
