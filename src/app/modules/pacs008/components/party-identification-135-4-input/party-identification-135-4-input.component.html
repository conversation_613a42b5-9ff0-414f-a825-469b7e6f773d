<!-- Nm -->
<app-cbpr-restricted-finx-max-140-text-extended-input
  [fieldName]="nameFieldName()"
  [label]="nameLabel()"
  [isReadOnly]="isReadOnly()"
/>
<!-- PstlAdr -->
<app-postal-address-24-2-input
  [departmentFieldName]="departmentFieldName()"
  [departmentLabel]="departmentLabel()"
  [subDepartmentFieldName]="subDepartmentFieldName()"
  [subDepartmentLabel]="subDepartmentLabel()"
  [streetNameFieldName]="streetNameFieldName()"
  [streetNameLabel]="streetNameLabel()"
  [buildingNumberFieldName]="buildingNumberFieldName()"
  [buildingNumberLabel]="buildingNumberLabel()"
  [buildingNameFieldName]="buildingNameFieldName()"
  [buildingNameLabel]="buildingNameLabel()"
  [floorFieldName]="floorFieldName()"
  [floorLabel]="floorLabel()"
  [postBoxFieldName]="postBoxFieldName()"
  [postBoxLabel]="postBoxLabel()"
  [roomFieldName]="roomFieldName()"
  [roomLabel]="roomLabel()"
  [postCodeFieldName]="postCodeFieldName()"
  [postCodeLabel]="postCodeLabel()"
  [townNameFieldName]="townNameFieldName()"
  [townNameLabel]="townNameLabel()"
  [townLocationNameFieldName]="townLocationNameFieldName()"
  [townLocationNameLabel]="townLocationNameLabel()"
  [districtNameFieldName]="districtNameFieldName()"
  [districtNameLabel]="districtNameLabel()"
  [countrySubdivisionFieldName]="countrySubdivisionFieldName()"
  [countrySubdivisionLabel]="countrySubdivisionLabel()"
  [countryFieldName]="countryFieldName()"
  [countryLabel]="countryLabel()"
  [addressLineFieldName]="addressLineFieldName()"
  [addressLineLabel]="addressLineLabel()"
  [isReadOnly]="isReadOnly()"
/>

<app-one-of-selector
  header="Choose between an organisation or an individual"
  i18n-header
  option1Label="Organisation"
  i18n-option1Label
  option2Label="Individual"
  i18n-option2Label
  [option1FieldNames]="[
    bicFieldName(),
    leiFieldName(),
    organisationIdFieldName(),
    organisationIdSchemeCodeFieldName(),
    proprietaryOrganisationIdSchemeFieldName(),
    organisationIdIssuerFieldName()
  ]"
  [option2FieldNames]="[
    birthDateFieldName(),
    provinceOfBirthFieldName(),
    cityOfBirthFieldName(),
    countryOfBirthFieldName(),
    individualIdFieldName(),
    individualIdSchemeCodeFieldName(),
    proprietaryIndividualIdSchemeFieldName(),
    individualIdIssuerFieldName()
  ]"
  [isReadOnly]="isReadOnly()"
>
  <ng-container slot="option1">
    <!-- Id-OrgId-AnyBIC -->
    <app-any-bic-dec-2014-identifier-input
      [fieldName]="bicFieldName()"
      [label]="bicLabel()"
      [isReadOnly]="isReadOnly()"
    />
    <!-- Id-OrgId-LEI -->
    <app-lei-identifier-input
      [label]="leiLabel()"
      [fieldName]="leiFieldName()"
      [isReadOnly]="isReadOnly()"
    />
    <app-hidden-section
      header="Other Identification"
      i18n-header
      [errorScopes]="[
        organisationIdFieldName(),
        organisationIdSchemeCodeFieldName(),
        proprietaryOrganisationIdSchemeFieldName(),
        organisationIdIssuerFieldName()
      ]"
    >
      <!-- Id-OrgId-Othr-Id -->
      <app-cbpr-restricted-finx-max-35-text-extended-input
        [fieldName]="organisationIdFieldName()"
        [label]="organisationIdLabel()"
        [isReadOnly]="isReadOnly()"
      />
      <app-one-of-selector
        header="Name of the Identification Schema"
        i18n-header
        option1Label="Code"
        i18n-option1Label
        option2Label="Proprietary"
        i18n-option2Label
        [option1FieldNames]="[organisationIdSchemeCodeFieldName()]"
        [option2FieldNames]="[proprietaryOrganisationIdSchemeFieldName()]"
        [isReadOnly]="isReadOnly()"
      >
        <ng-container slot="option1">
          <!-- Id-OrgId-Othr-SchmeNm-Cd -->
          <app-external-organisation-identification-1-code-input
            [fieldName]="organisationIdSchemeCodeFieldName()"
            [label]="organisationIdSchemeCodeLabel()"
            [isReadOnly]="isReadOnly()"
          />
        </ng-container>
        <ng-container slot="option2">
          <!-- Id-OrgId-Othr-SchmeNm-Prtry -->
          <app-cbpr-restricted-finx-max-35-text-extended-input
            [fieldName]="proprietaryOrganisationIdSchemeFieldName()"
            [label]="proprietaryOrganisationIdSchemeLabel()"
            [isReadOnly]="isReadOnly()"
          />
        </ng-container>
      </app-one-of-selector>
      <!-- Id-OrgId-Othr-Issr -->
      <app-cbpr-restricted-finx-max-35-text-extended-input
        [fieldName]="organisationIdIssuerFieldName()"
        [label]="organisationIdIssuerLabel()"
        [isReadOnly]="isReadOnly()"
      />
    </app-hidden-section>
  </ng-container>
  <ng-container slot="option2">
    <!-- Id-PrvtId-DtAndPlcOfBirth -->
    <app-date-and-place-of-birth-1-1-input
      [birthDateFieldName]="birthDateFieldName()"
      [birthDateLabel]="birthDateLabel()"
      [provinceOfBirthFieldName]="provinceOfBirthFieldName()"
      [provinceOfBirthLabel]="provinceOfBirthLabel()"
      [cityOfBirthFieldName]="cityOfBirthFieldName()"
      [cityOfBirthLabel]="cityOfBirthLabel()"
      [countryOfBirthFieldName]="countryOfBirthFieldName()"
      [countryOfBirthLabel]="countryOfBirthLabel()"
      [isReadOnly]="isReadOnly()"
    />
    <app-hidden-section
      header="Other Identification"
      i18n-header
      [errorScopes]="[
        individualIdFieldName(),
        individualIdSchemeCodeFieldName(),
        proprietaryIndividualIdSchemeFieldName(),
        individualIdIssuerFieldName()
      ]"
    >
      <!-- Id-PrvtId-Othr-Id -->
      <app-cbpr-restricted-finx-max-35-text-extended-input
        [fieldName]="individualIdFieldName()"
        [label]="individualIdLabel()"
        [isReadOnly]="isReadOnly()"
      />
      <app-one-of-selector
        header="Name of the Identification Schema"
        i18n-header
        option1Label="Code"
        i18n-option1Label
        option2Label="Proprietary"
        i18n-option2Label
        [option1FieldNames]="[individualIdSchemeCodeFieldName()]"
        [option2FieldNames]="[proprietaryIndividualIdSchemeFieldName()]"
        [isReadOnly]="isReadOnly()"
      >
        <ng-container slot="option1">
          <!-- Id-PrvtId-Othr-SchmeNm-Cd -->
          <app-external-person-identification-1-code-input
            [fieldName]="individualIdSchemeCodeFieldName()"
            [label]="individualIdSchemeCodeLabel()"
            [isReadOnly]="isReadOnly()"
          />
        </ng-container>
        <ng-container slot="option2">
          <!-- Id-PrvtId-Othr-SchmeNm-Prtry -->
          <app-cbpr-restricted-finx-max-35-text-extended-input
            [fieldName]="proprietaryIndividualIdSchemeFieldName()"
            [label]="proprietaryIndividualIdSchemeLabel()"
            [isReadOnly]="isReadOnly()"
          />
        </ng-container>
      </app-one-of-selector>
      <!-- Id-PrvtId-Othr-Issr -->
      <app-cbpr-restricted-finx-max-35-text-extended-input
        [fieldName]="individualIdIssuerFieldName()"
        [label]="individualIdIssuerLabel()"
        [isReadOnly]="isReadOnly()"
      />
    </app-hidden-section>
  </ng-container>
</app-one-of-selector>

<!-- CtryOfRes -->
<app-country-code-input
  [fieldName]="countryOfResidenceFieldName()"
  [label]="countryOfResidenceLabel()"
  [isReadOnly]="isReadOnly()"
/>
