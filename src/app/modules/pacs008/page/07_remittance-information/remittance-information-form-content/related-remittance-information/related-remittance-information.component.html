<app-hidden-section
  header="Related Remittance Information"
  i18n-header
  [errorScopes]="['FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf']"
>
  <app-imb-form-text-input
    [label]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-Mtd']
    "
    fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-Mtd"
    [isReadOnly]="isReadOnly()"
  />
  <app-imb-form-text-input
    [label]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-ElctrncAdr'
      ]
    "
    fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-ElctrncAdr"
    [isReadOnly]="isReadOnly()"
  />
  <app-cbpr-restricted-finx-max-140-text-extended-input
    fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Nm"
    [label]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Nm'
      ]
    "
    [isReadOnly]="isReadOnly()"
  />
  <app-postal-address-24-1-input
    departmentFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Dept"
    [departmentLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Dept'
      ]
    "
    subDepartmentFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-SubDept"
    [subDepartmentLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-SubDept'
      ]
    "
    streetNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-StrtNm"
    [streetNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-StrtNm'
      ]
    "
    buildingNumberFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-BldgNb"
    [buildingNumberLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-BldgNb'
      ]
    "
    buildingNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-BldgNm"
    [buildingNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-BldgNm'
      ]
    "
    floorFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Flr"
    [floorLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Flr'
      ]
    "
    postBoxFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-PstBx"
    [postBoxLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-PstBx'
      ]
    "
    roomFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Room"
    [roomLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Room'
      ]
    "
    postCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-PstCd"
    [postCodeLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-PstCd'
      ]
    "
    townNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-TwnNm"
    [townNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-TwnNm'
      ]
    "
    townLocationNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-TwnLctnNm"
    [townLocationNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-TwnLctnNm'
      ]
    "
    districtNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-DstrctNm"
    [districtNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-DstrctNm'
      ]
    "
    countrySubdivisionFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-CtrySubDvsn"
    [countrySubdivisionLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-CtrySubDvsn'
      ]
    "
    countryFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Ctry"
    [countryLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Ctry'
      ]
    "
    addressLineFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-AdrLine"
    [addressLineLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-AdrLine'
      ]
    "
    [isReadOnly]="isReadOnly()"
  />
</app-hidden-section>
