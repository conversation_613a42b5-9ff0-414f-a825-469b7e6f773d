import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { IMBFormTextInputComponent } from '@shared/ui';

@Component({
  selector: 'app-cbpr-restricted-finx-max-16-text-extended-input',
  imports: [IMBFormTextInputComponent],
  templateUrl:
    './cbpr-restricted-finx-max-16-text-extended-input.component.html',
  styleUrl: './cbpr-restricted-finx-max-16-text-extended-input.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CBPR_RestrictedFINXMax16Text_ExtendedInputComponent {
  fieldName = input.required<string>();
  label = input.required<string>();
  isReadOnly = input.required<boolean>();
}
