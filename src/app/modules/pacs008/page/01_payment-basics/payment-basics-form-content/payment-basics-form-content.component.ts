import {
  ChangeDetectionStrategy,
  Component,
  computed,
  input,
} from '@angular/core';
import {
  FormPageContentComponent,
  HiddenSectionComponent,
  OneOfSelectorComponent,
  IMBFormTextInputComponent,
  IMBFormRadioButtonGroupComponent,
} from '@shared/ui';
import { FORM_METADATA } from '../../../../../generated/pacs008-form-metadata';
import { CBPR_RestrictedFINXMax35TextInputComponent } from '../../../components';
import {
  ClearingChannel2Code,
  clearingChannel2Codes,
  Priority2Code,
  priority2Codes,
} from '@shared/types';
import { SelectOption } from '@helaba/iso20022-lib/util';

@Component({
  selector: 'app-payment-basics-form-content',
  imports: [
    IMBFormTextInputComponent,
    CBPR_RestrictedFINXMax35TextInputComponent,
    IMBFormRadioButtonGroupComponent,
    HiddenSectionComponent,
    OneOfSelectorComponent,
  ],
  templateUrl: './payment-basics-form-content.component.html',
  styleUrl: './payment-basics-form-content.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PaymentBasicsFormContentComponent extends FormPageContentComponent {
  override pageKey = input.required<string>();
  isReadOnly = input.required<boolean>();

  override labels = computed(() => FORM_METADATA[this.pageKey()].labels);

  settlementPriorityOptions: SelectOption<Priority2Code>[] = priority2Codes.map(
    (code) => ({
      key: code,
      label: code,
      value: code,
    })
  );
  clearingChannelOptions: SelectOption<ClearingChannel2Code>[] =
    clearingChannel2Codes.map((code) => ({
      key: code,
      label: code,
      value: code,
    }));
}
