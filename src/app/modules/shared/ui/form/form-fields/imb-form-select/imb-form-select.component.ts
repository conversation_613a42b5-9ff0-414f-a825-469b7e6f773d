import {
  ChangeDetectionStrategy,
  Component,
  computed,
  input,
  output,
  Type,
} from '@angular/core';
import {
  ControlContainer,
  FormGroupDirective,
  ReactiveFormsModule,
} from '@angular/forms';
import { FormFieldErrorsComponent } from '../../form-field-errors';
import { PlainFieldsetComponent } from '../../form-structures';
import { IMBFormValueComponent } from '../../imb-form-value';
import { SelectModule } from 'primeng/select';
import { FormSingleInputComponent } from '@helaba/iso20022-lib/components';
import { SelectOption } from '@helaba/iso20022-lib/util';

@Component({
  selector: 'app-imb-form-select',
  imports: [
    ReactiveFormsModule,
    FormSingleInputComponent,
    SelectModule,
    FormFieldErrorsComponent,
    IMBFormValueComponent,
  ],
  templateUrl: './imb-form-select.component.html',
  styleUrl: './imb-form-select.component.scss',
  viewProviders: [
    { provide: ControlContainer, useExisting: FormGroupDirective }, // When resolving 'ControlContainer', just use the parent form that was declared up the tree, required to use 'formControlName' within the component.
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class IMBFormSelectComponent<T> {
  label = input.required<string>();
  fieldName = input.required<string>();
  isReadOnly = input.required<boolean>();
  fieldPrefix = input<string>('');

  options = input.required<SelectOption<T>[]>();
  placeholder = input.required<string>();
  editable = input<boolean>(false);
  filter = input<boolean>(false);

  fieldsetComponent: Type<PlainFieldsetComponent> = PlainFieldsetComponent;
}
