<app-form-single-input
  [label]="label()"
  [fieldName]="fieldName()"
  [isReadOnly]="isReadOnly()"
  [fieldTemplate]="fieldTemplate"
  [errorTemplate]="errorTemplate"
  [formValueFieldTemplate]="formValueFieldTemplate"
  [fieldPrefix]="fieldPrefix()"
  [fieldsetComponent]="fieldsetComponent"
/>

<ng-template #fieldTemplate let-fieldId let-hasError="hasError">
  <p-inputnumber
    [inputId]="fieldId"
    [formControlName]="fieldName()"
    [showButtons]="true"
    [min]="0"
    [max]="max()"
    [maxFractionDigits]="maxFractionDigits()"
    (onInput)="onInput($event)"
  />
</ng-template>

<ng-template #errorTemplate let-fieldId>
  <app-form-field-errors [fieldName]="fieldName()" [fieldId]="fieldId" />
</ng-template>

<ng-template
  #formValueFieldTemplate
  let-fieldName
  let-formControl="formControl"
>
  <app-imb-form-value [fieldName]="fieldName" [formControl]="formControl" />
</ng-template>
