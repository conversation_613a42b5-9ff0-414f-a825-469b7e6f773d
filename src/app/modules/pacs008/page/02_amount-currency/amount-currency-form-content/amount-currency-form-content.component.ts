import {
  ChangeDetectionStrategy,
  Component,
  computed,
  input,
} from '@angular/core';
import {
  FormPageContentComponent,
  HiddenSectionComponent,
  IMBFormTextInputComponent,
  IMBFormRadioButtonGroupComponent,
  IMBFormGroupArrayComponent,
} from '@shared/ui';
import { FORM_METADATA } from '../../../../../generated/pacs008-form-metadata';
import {
  BranchAndFinancialInstitutionIdentification6__1InputComponent,
  CBPR_Amount__1InputComponent,
} from '../../../components';
import { ChargeBearerType1Code, chargeBearerType1Codes } from '@shared/types';
import { SelectOption } from '@helaba/iso20022-lib/util';

@Component({
  selector: 'app-amount-currency-form-content',
  imports: [
    CBPR_Amount__1InputComponent,
    IMBFormTextInputComponent,
    IMBFormRadioButtonGroupComponent,
    BranchAndFinancialInstitutionIdentification6__1InputComponent,
    HiddenSectionComponent,
    IMBFormGroupArrayComponent,
  ],
  templateUrl: './amount-currency-form-content.component.html',
  styleUrl: './amount-currency-form-content.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AmountCurrencyFormContentComponent extends FormPageContentComponent {
  override pageKey = input.required<string>();
  isReadOnly = input.required<boolean>();

  override labels = computed(() => FORM_METADATA[this.pageKey()].labels);

  // Options for select fields
  chargeBearerOptions: SelectOption<ChargeBearerType1Code>[] =
    chargeBearerType1Codes.map((code) => ({
      key: code,
      label: code,
      value: code,
    }));
}
