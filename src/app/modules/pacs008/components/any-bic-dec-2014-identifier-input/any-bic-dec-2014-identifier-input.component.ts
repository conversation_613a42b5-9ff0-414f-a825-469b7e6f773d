import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { IMBFormTextInputComponent } from '@shared/ui';

@Component({
  selector: 'app-any-bic-dec-2014-identifier-input',
  imports: [IMBFormTextInputComponent],
  templateUrl: './any-bic-dec-2014-identifier-input.component.html',
  styleUrl: './any-bic-dec-2014-identifier-input.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AnyBICDec2014IdentifierInputComponent {
  fieldName = input.required<string>();
  label = input.required<string>();
  isReadOnly = input.required<boolean>();
}
