import {
  BasicRule,
  Condition,
  isCondition,
  NestedCondition,
  Rule,
} from '@helaba/iso20022-lib/rules';

/**
 * Filter the rules down to those that are relevant to the given fields, i.e. those where the target and condition only reference relevant fields.
 * @param allRules all rules to filter
 * @param relevantFields fields that are relevant for the rules
 * @returns filtered rules that are relevant to the given fields
 */
export function filterRules(
  allRules: Rule[],
  relevantFields: string[]
): Rule[] {
  return allRules.filter((rule) => {
    if (rule.type === 'serverOnly') {
      throw new Error(
        'Server-only rules should not be passed to the client-side.'
      );
    }
    if (rule.type === 'condition') {
      const allConditionsReferenceRelevantFields = rule.conditions.every(
        (condition: NestedCondition) =>
          isCondition(condition)
            ? conditionOnlyReferencesRelevantFields(condition, relevantFields)
            : condition.conditions.every((nestedCondition) =>
                conditionOnlyReferencesRelevantFields(
                  nestedCondition,
                  relevantFields
                )
              )
      );

      if (!allConditionsReferenceRelevantFields) {
        return false;
      }
      return rule.rules.every((subRule) =>
        basicRuleOnlyReferencesRelevantFields(subRule, relevantFields)
      );
    }

    return basicRuleOnlyReferencesRelevantFields(rule, relevantFields);
  });
}

function conditionOnlyReferencesRelevantFields(
  condition: Condition,
  relevantFields: string[]
): boolean {
  const mainFieldIsRelevant = isIncluded(condition.field, relevantFields);

  if (condition.type === 'notEqual') {
    const otherFieldIsRelevant = isIncluded(
      condition.otherField,
      relevantFields
    );
    return mainFieldIsRelevant && otherFieldIsRelevant;
  }

  return mainFieldIsRelevant;
}

function basicRuleOnlyReferencesRelevantFields(
  rule: BasicRule,
  relevantFields: string[]
): boolean {
  const targetIsRelevant = isIncluded(rule.target, relevantFields);

  if (rule.type === 'contains') {
    const otherFieldsAreRelevant = rule.value.every((otherField) =>
      isIncluded(otherField, relevantFields)
    );
    return targetIsRelevant && otherFieldsAreRelevant;
  }

  return targetIsRelevant;
}

function isIncluded(target: string, fields: string[]): boolean {
  return fields.some((field) => field.startsWith(target));
}
