import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { SelectOption } from '@helaba/iso20022-lib/util';
import { IMBFormRadioButtonGroupComponent } from '@shared/ui';

const trueFalseValues = ['true', 'false', 'SHAR'] as const;
type TrueFalseValue = (typeof trueFalseValues)[number];

@Component({
  selector: 'app-true-false-indicator-input',
  imports: [IMBFormRadioButtonGroupComponent],
  templateUrl: './true-false-indicator-input.component.html',
  styleUrl: './true-false-indicator-input.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TrueFalseIndicatorInputComponent {
  fieldName = input.required<string>();
  label = input.required<string>();
  isReadOnly = input.required<boolean>();

  trueFalseOptions: SelectOption<TrueFalseValue>[] = trueFalseValues.map(
    (code) => ({
      key: code,
      label: code,
      value: code,
    })
  );
}
