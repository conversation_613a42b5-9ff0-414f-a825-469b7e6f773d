import {
  ChangeDetectionStrategy,
  Component,
  computed,
  effect,
  inject,
  input,
  signal,
} from '@angular/core';
import { FieldsetModule } from 'primeng/fieldset';
import { PanelModule } from 'primeng/panel';
import { SelectButtonModule } from 'primeng/selectbutton';
import { ControlContainer, FormGroup, FormsModule } from '@angular/forms';
import { FormFieldErrorsComponent } from '../../form-field-errors';
import { Subscription } from 'rxjs';
import {
  getCurrentFormLevelFieldName,
  getFieldIdsFromFormValues,
  isPresent,
  SelectOption,
} from '@helaba/iso20022-lib/util';
import { SelectButtonComponent } from '../select-button';

const OPTION1_VALUE = 'option1';
const OPTION2_VALUE = 'option2';

function resetField(fieldName: string, formGroup: FormGroup) {
  const fieldControl = formGroup.get(fieldName);
  if (!fieldControl) {
    throw new Error(
      `Cannot find control for field name "${fieldName}" in form group.`
    );
  }
  fieldControl.reset();
  fieldControl.markAsTouched();
}

@Component({
  selector: 'app-one-of-selector',
  imports: [
    FieldsetModule,
    SelectButtonModule,
    FormsModule,
    FormFieldErrorsComponent,
    SelectButtonComponent,
    PanelModule,
  ],
  templateUrl: './one-of-selector.component.html',
  styleUrl: './one-of-selector.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class OneOfSelectorComponent {
  readonly OPTION1 = OPTION1_VALUE;
  readonly OPTION2 = OPTION2_VALUE;

  header = input.required<string>();
  option1Label = input.required<string>();
  option2Label = input.required<string>();
  option1FieldNames = input.required<string[]>();
  option2FieldNames = input.required<string[]>();
  isReadOnly = input.required<boolean>();
  fieldPrefix = input<string>(''); // Contains the upstream form structure including dots and array indices.

  controlContainer = inject(ControlContainer);

  // If the OneOfSelector is used inside a form group array, this is only the local form group of the current array item.
  get formGroup(): FormGroup {
    return this.controlContainer.control as FormGroup;
  }

  private currentFormValue = signal(null);
  #activeSubscriptions: Subscription[] = [];

  constructor() {
    // Track value changes of the form group such that we can recompute 'allFieldIdsWithFieldNames' below.
    effect((onCleanup) => {
      // Clean up any existing subscriptions when the formGroup changes or component is destroyed.
      this.cleanup();

      if (!this.formGroup) {
        throw new Error('Form group is not defined.');
      }

      this.#activeSubscriptions.push(
        this.formGroup.valueChanges.subscribe((currentValues) => {
          this.currentFormValue.set(currentValues);
        })
      );

      onCleanup(() => {
        this.cleanup();
      });
    });
  }

  allFieldNames = computed<string[]>(() => {
    return [...this.option1FieldNames(), ...this.option2FieldNames()];
  });

  // Compute field IDs from the current form group for the field names of the one of selector.
  allFieldIds = computed<string[]>(() => {
    // If the OneOfSelector is used inside a form group array, these are only the values of the current array item's form group.
    const currentFormValue = this.currentFormValue();
    // Thus, the field IDs are only the local form group field IDs.
    const currentFormGroupFieldIds =
      getFieldIdsFromFormValues(currentFormValue);

    return currentFormGroupFieldIds.filter((fieldId) => {
      const currentFormLevelFieldName = getCurrentFormLevelFieldName(fieldId);
      return this.allFieldNames().includes(currentFormLevelFieldName);
    });
  });

  getGlobalFieldId(localFieldId: string): string {
    // If the OneOfSelector is used inside a form group array, we add the field prefix to retrieve the field ID that is globally unique in the parent form group.
    return `${this.fieldPrefix()}${
      this.fieldPrefix() ? '.' : ''
    }${localFieldId}`;
  }

  selectButtonOptions = computed<SelectOption<string>[]>(() => {
    return [
      {
        key: this.option1Label(),
        label: this.option1Label(),
        value: this.OPTION1,
      },
      {
        key: this.option2Label(),
        label: this.option2Label(),
        value: this.OPTION2,
      },
    ];
  });

  selectedOption = signal<string | null>(null);

  onChangeSelectedOption(newSelectedOption: string) {
    this.selectedOption.set(newSelectedOption);

    if (newSelectedOption === this.OPTION1) {
      for (const fieldName of this.option2FieldNames()) {
        resetField(fieldName, this.formGroup);
      }
    } else if (newSelectedOption === this.OPTION2) {
      for (const fieldName of this.option1FieldNames()) {
        resetField(fieldName, this.formGroup);
      }
    }
  }

  // Compute whether the user has filled out option 1 or option 2 fields (or none) for readonly display.
  hasFilledOutOption1Fields = computed(() => {
    return this.option1FieldNames().some((fieldName) => {
      const fieldControl = this.formGroup.get(fieldName);
      return isPresent(fieldControl?.value);
    });
  });

  hasFilledOutOption2Fields = computed(() => {
    return this.option2FieldNames().some((field) => {
      const fieldControl = this.formGroup.get(field);
      return isPresent(fieldControl?.value);
    });
  });

  private cleanup() {
    this.#activeSubscriptions.forEach((sub) => sub.unsubscribe());
    this.#activeSubscriptions = [];
  }
}
