import { Injectable, signal } from '@angular/core';
import { FormArray, FormControl, FormGroup } from '@angular/forms';

/**
 * Store the individual FormGroup data for each page of a multi-step form.
 * The data is updated for every page change. This way, the review page can access the data.
 */
@Injectable({
  providedIn: 'root',
})
export class FormMergeService {
  #mergedFormData = signal<Record<string, FormGroup>>({});
  mergedFormData = this.#mergedFormData.asReadonly();

  /**
   * Merge all page data into a single object
   */
  setPageData(pageKey: string, formGroup: FormGroup): void {
    this.#mergedFormData.set({
      ...this.mergedFormData(),
      [pageKey]: formGroup,
    });
  }
}
