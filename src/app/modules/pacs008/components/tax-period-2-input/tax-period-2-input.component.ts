import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { ISODateInputComponent } from '../iso-date-input';
import { IMBFormTextInputComponent } from '@shared/ui';

@Component({
  selector: 'app-tax-period-2-input',
  imports: [ISODateInputComponent, IMBFormTextInputComponent],
  templateUrl: './tax-period-2-input.component.html',
  styleUrl: './tax-period-2-input.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TaxPeriod2InputComponent {
  yearFieldName = input.required<string>();
  yearLabel = input.required<string>();
  typeFieldName = input.required<string>();
  typeLabel = input.required<string>();
  fromDateFieldName = input.required<string>();
  fromDateLabel = input.required<string>();
  toDateFieldName = input.required<string>();
  toDateLabel = input.required<string>();

  isReadOnly = input.required<boolean>();
}
