<app-one-of-selector
  header="Account Identification"
  i18n-header
  option1Label="IBAN"
  i18n-option1Label
  option2Label="Other Identification"
  i18n-option2Label
  [option1FieldNames]="[ibanFieldName()]"
  [option2FieldNames]="[
    idFieldName(),
    identificationSchemeNameCodeFieldName(),
    proprietaryIdentificationSchemeNameFieldName(),
    identificationIssuerFieldName()
  ]"
  [isReadOnly]="isReadOnly()"
>
  <ng-container slot="option1">
    <!-- Id-IBAN -->
    <app-imb-form-text-input
      [fieldName]="ibanFieldName()"
      [label]="ibanLabel()"
      [isReadOnly]="isReadOnly()"
    />
  </ng-container>
  <ng-container slot="option2">
    <!-- Id-Othr-Id -->
    <app-imb-form-text-input
      [fieldName]="idFieldName()"
      [label]="idLabel()"
      [isReadOnly]="isReadOnly()"
    />
    <!-- Id-Othr-SchmeNm-Cd -->
    <app-imb-form-text-input
      [fieldName]="identificationSchemeNameCodeFieldName()"
      [label]="identificationSchemeNameCodeLabel()"
      [isReadOnly]="isReadOnly()"
    />
    <!-- Id-Othr-SchmeNm-Prtry -->
    <app-cbpr-restricted-finx-max-35-text-input
      [fieldName]="proprietaryIdentificationSchemeNameFieldName()"
      [label]="proprietaryIdentificationSchemeNameLabel()"
      [isReadOnly]="isReadOnly()"
    />
    <!-- Id-Othr-Issr -->
    <app-cbpr-restricted-finx-max-35-text-input
      [fieldName]="identificationIssuerFieldName()"
      [label]="identificationIssuerLabel()"
      [isReadOnly]="isReadOnly()"
    />
  </ng-container>
</app-one-of-selector>
<app-one-of-selector
  header="Account-Typ"
  option1Label="Code"
  option2Label="Proprietär"
  [option1FieldNames]="[typeCodeFieldName()]"
  [option2FieldNames]="[proprietaryTypeFieldName()]"
  [isReadOnly]="isReadOnly()"
>
  <ng-container slot="option1">
    <!-- Tp-Cd -->
    <app-imb-form-text-input
      [fieldName]="typeCodeFieldName()"
      [label]="typeCodeLabel()"
      [isReadOnly]="isReadOnly()"
    />
  </ng-container>
  <ng-container slot="option2">
    <!-- Tp-Prtry -->
    <app-cbpr-restricted-finx-max-35-text-input
      [fieldName]="proprietaryTypeFieldName()"
      [label]="proprietaryTypeLabel()"
      [isReadOnly]="isReadOnly()"
    />
  </ng-container>
</app-one-of-selector>
<!-- Ccy -->
<app-active-or-historic-currency-code-input
  [fieldName]="currencyFieldName()"
  [label]="currencyLabel()"
  [isReadOnly]="isReadOnly()"
/>
<!-- Nm -->
<app-imb-form-text-input
  [fieldName]="nameFieldName()"
  [label]="nameLabel()"
  [isReadOnly]="isReadOnly()"
/>
<app-hidden-section
  header="Proxy"
  [errorScopes]="[
    proxyTypeCodeFieldName(),
    proprietaryProxyTypeFieldName(),
    proxyIdFieldName()
  ]"
>
  <app-one-of-selector
    header="Proxy-Typ"
    option1Label="Code"
    option2Label="Proprietär"
    [option1FieldNames]="[proxyTypeCodeFieldName()]"
    [option2FieldNames]="[proprietaryProxyTypeFieldName()]"
    [isReadOnly]="isReadOnly()"
  >
    <ng-container slot="option1">
      <!-- Prxy-Tp-Cd -->
      <app-imb-form-text-input
        [fieldName]="proxyTypeCodeFieldName()"
        [label]="proxyTypeCodeLabel()"
        [isReadOnly]="isReadOnly()"
      />
    </ng-container>
    <ng-container slot="option2">
      <!-- Prxy-Tp-Prtry -->
      <app-cbpr-restricted-finx-max-35-text-input
        [fieldName]="proprietaryProxyTypeFieldName()"
        [label]="proprietaryProxyTypeLabel()"
        [isReadOnly]="isReadOnly()"
      />
    </ng-container>
  </app-one-of-selector>
  <!-- Prxy-Id -->
  <app-imb-form-text-input
    [fieldName]="proxyIdFieldName()"
    [label]="proxyIdLabel()"
    [isReadOnly]="isReadOnly()"
  />
</app-hidden-section>
