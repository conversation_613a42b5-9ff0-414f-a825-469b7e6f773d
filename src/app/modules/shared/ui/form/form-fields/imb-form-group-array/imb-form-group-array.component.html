<app-form-group-array
  [label]="label()"
  [fieldName]="fieldName()"
  [isReadOnly]="isReadOnly()"
  [fieldPrefix]="fieldPrefix()"
  [errorTemplate]="errorTemplate"
  [formValueFieldTemplate]="formValueFieldTemplate"
  [addItemButtonTemplate]="addItemButtonTemplate"
  [removeItemButtonTemplate]="removeItemButtonTemplate"
  [groupFactory]="groupFactory()"
  [itemTemplate]="itemTemplate()"
  [validationRules]="validationRules()"
  [fieldsetComponent]="fieldsetComponent"
/>

<ng-template #errorTemplate let-fieldId>
  <app-form-field-errors [fieldName]="fieldName()" [fieldId]="fieldId" />
</ng-template>

<ng-template
  #formValueFieldTemplate
  let-fieldName
  let-formControl="formControl"
>
  <app-imb-form-value [fieldName]="fieldName" [formControl]="formControl" />
</ng-template>

<ng-template #addItemButtonTemplate let-addItem>
  <p-button icon="pi pi-plus" (click)="addItem()" label="Add Item" />
</ng-template>

<ng-template #removeItemButtonTemplate let-removeItem let-index="index">
  <p-button
    icon="pi pi-times"
    [rounded]="true"
    [text]="true"
    severity="danger"
    (click)="removeItem(index)"
  />
</ng-template>
