<app-imb-form-text-input
  fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-InstrId"
  [label]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-InstrId']"
  [isReadOnly]="isReadOnly()"
/>
<app-cbpr-restricted-finx-max-35-text-input
  fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-EndToEndId"
  [label]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-EndToEndId']"
  [isReadOnly]="isReadOnly()"
/>
<app-cbpr-restricted-finx-max-35-text-input
  fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-TxId"
  [label]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-TxId']"
  [isReadOnly]="isReadOnly()"
/>
<app-hidden-section
  header="Payment Type Information"
  i18n-header
  [errorScopes]="['FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf']"
>
  <app-imb-form-radio-button-group
    [label]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-InstrPrty']"
    fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-InstrPrty"
    [options]="settlementPriorityOptions"
    [isReadOnly]="isReadOnly()"
  />

  <!-- TODO: This needs to be in an array -->
  <app-one-of-selector
    header="Service Level"
    i18n-header
    option1Label="Service Level Code"
    i18n-option1Label
    option2Label="Proprietary Service Level"
    i18n-option2Label
    [option1FieldNames]="['FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl-Cd']"
    [option2FieldNames]="[
      'FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl-Prtry'
    ]"
    [isReadOnly]="isReadOnly()"
  >
    <ng-container slot="option1">
      <app-imb-form-text-input
        fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl-Cd"
        [label]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl-Cd']"
        [isReadOnly]="isReadOnly()"
      />
    </ng-container>
    <ng-container slot="option2">
      <app-cbpr-restricted-finx-max-35-text-input
        fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl-Prtry"
        [label]="
          labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl-Prtry']
        "
        [isReadOnly]="isReadOnly()"
      />
    </ng-container>
  </app-one-of-selector>

  <app-one-of-selector
    header="Category Purpose"
    i18n-header
    option1Label="Category Purpose Code"
    i18n-option1Label
    option2Label="Proprietary Category Purpose"
    i18n-option2Label
    [option1FieldNames]="['FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-CtgyPurp-Cd']"
    [option2FieldNames]="[
      'FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-CtgyPurp-Prtry'
    ]"
    [isReadOnly]="isReadOnly()"
  >
    <ng-container slot="option1">
      <app-imb-form-text-input
        fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-CtgyPurp-Cd"
        [label]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-CtgyPurp-Cd']"
        [isReadOnly]="isReadOnly()"
      />
    </ng-container>
    <ng-container slot="option2">
      <app-cbpr-restricted-finx-max-35-text-input
        fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-CtgyPurp-Prtry"
        [label]="
          labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-CtgyPurp-Prtry']
        "
        [isReadOnly]="isReadOnly()"
      />
    </ng-container>
  </app-one-of-selector>

  <app-imb-form-radio-button-group
    [label]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-ClrChanl']"
    fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-ClrChanl"
    [options]="clearingChannelOptions"
    [isReadOnly]="isReadOnly()"
  />

  <app-one-of-selector
    header="Local Instrument"
    i18n-header
    option1Label="Local Instrument Code"
    i18n-option1Label
    option2Label="Proprietary Local Instrument"
    i18n-option2Label
    [option1FieldNames]="[
      'FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-LclInstrm-Cd'
    ]"
    [option2FieldNames]="[
      'FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-LclInstrm-Prtry'
    ]"
    [isReadOnly]="isReadOnly()"
  >
    <ng-container slot="option1">
      <app-imb-form-text-input
        fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-LclInstrm-Cd"
        [label]="
          labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-LclInstrm-Cd']
        "
        [isReadOnly]="isReadOnly()"
      />
    </ng-container>
    <ng-container slot="option2">
      <app-cbpr-restricted-finx-max-35-text-input
        fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-LclInstrm-Prtry"
        [label]="
          labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-LclInstrm-Prtry']
        "
        [isReadOnly]="isReadOnly()"
      />
    </ng-container>
  </app-one-of-selector>
</app-hidden-section>

<app-one-of-selector
  header="Purpose"
  i18n-header
  option1Label="Purpose Code"
  i18n-option1Label
  option2Label="Proprietary Purpose"
  i18n-option2Label
  [option1FieldNames]="['FIToFICstmrCdtTrf-CdtTrfTxInf-Purp-Cd']"
  [option2FieldNames]="['FIToFICstmrCdtTrf-CdtTrfTxInf-Purp-Prtry']"
  [isReadOnly]="isReadOnly()"
>
  <ng-container slot="option1">
    <app-imb-form-text-input
      fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Purp-Cd"
      [label]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Purp-Cd']"
      [isReadOnly]="isReadOnly()"
    />
  </ng-container>
  <ng-container slot="option2">
    <app-cbpr-restricted-finx-max-35-text-input
      fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Purp-Prtry"
      [label]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Purp-Prtry']"
      [isReadOnly]="isReadOnly()"
    />
  </ng-container>
</app-one-of-selector>
