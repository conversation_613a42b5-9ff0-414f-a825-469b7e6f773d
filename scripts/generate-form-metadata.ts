import * as fs from 'fs';
import * as path from 'path';
import * as ts from 'typescript';
import { filterRules } from '../src/app/modules/shared/rules';
import type { FormMetadata } from '../src/app/modules/shared/types';
import { i18nLabelGroupsSchema } from './i18n-label-groups.schema';
import { clientRules, formSetup, Rule } from '@helaba/iso20022-lib/rules';
import { splitPascalCase } from '@helaba/iso20022-lib/util';

const FORM_CONFIGS: {
  pageKey:
    | 'paymentBasics'
    | 'amountCurrency'
    | 'settlementInformation'
    | 'debtorInformation'
    | 'creditorInformation'
    | 'financialInstitutionInformation'
    | 'remittanceInformation'
    | 'regulatoryReporting';
  schemaPath: string;
}[] = [
  {
    pageKey: 'paymentBasics',
    schemaPath:
      './src/app/modules/pacs008/page/01_payment-basics/payment-basics.form.ts',
  },
  {
    pageKey: 'amountCurrency',
    schemaPath:
      './src/app/modules/pacs008/page/02_amount-currency/amount-currency.form.ts',
  },
  {
    pageKey: 'settlementInformation',
    schemaPath:
      './src/app/modules/pacs008/page/03_settlement-information/settlement-information.form.ts',
  },
  {
    pageKey: 'debtorInformation',
    schemaPath:
      './src/app/modules/pacs008/page/04_debtor-information/debtor-information.form.ts',
  },
  {
    pageKey: 'creditorInformation',
    schemaPath:
      './src/app/modules/pacs008/page/05_creditor-information/creditor-information.form.ts',
  },
  {
    pageKey: 'financialInstitutionInformation',
    schemaPath:
      './src/app/modules/pacs008/page/06_financial-institution-information/financial-institution-information.form.ts',
  },
  {
    pageKey: 'remittanceInformation',
    schemaPath:
      './src/app/modules/pacs008/page/07_remittance-information/remittance-information.form.ts',
  },
  {
    pageKey: 'regulatoryReporting',
    schemaPath:
      './src/app/modules/pacs008/page/08_regulatory-reporting/regulatory-reporting.form.ts',
  },
];

function validateFormSetupMatchesSchemas(): void {
  for (const config of FORM_CONFIGS) {
    const formFields = extractFormFields(config.schemaPath);

    const pageKey = config.pageKey;
    const visibleSetupFields = Object.keys(formSetup[pageKey].visible);
    const serverOnlySetupFields = Object.keys(formSetup[pageKey].serverOnly);

    for (const visibleField of visibleSetupFields) {
      if (!formFields.includes(visibleField)) {
        throw new Error(
          `Field "${visibleField}" is not present on page "${pageKey}" as defined by the form setup.`
        );
      }
    }

    for (const field of formFields) {
      if (
        !visibleSetupFields.includes(field) &&
        !serverOnlySetupFields.includes(field)
      ) {
        throw new Error(
          `Field "${field}" is present on page "${pageKey}" but not defined in the form setup.`
        );
      }
    }
  }
}

function validateLabelGroupsMatchFormSetup(
  i18nLabelGroups: Record<string, string[]>
): void {
  const allFieldsFromFormSetup = new Set<string>();
  for (const fields of Object.values(formSetup)) {
    const visibleFields = Object.keys(fields.visible);
    const serverOnlyFields = Object.keys(fields.serverOnly);
    for (const field of [...visibleFields, ...serverOnlyFields]) {
      allFieldsFromFormSetup.add(field);
    }
  }

  for (const [label, fields] of Object.entries(i18nLabelGroups)) {
    for (const field of fields) {
      if (!allFieldsFromFormSetup.has(field)) {
        throw new Error(
          `Field "${field}" in label group "${label}" does not match any field in form setup.`
        );
      }
    }
  }
}

function validateLabelGroupsDoNotContainDuplicates(
  i18nLabelGroups: Record<string, string[]>
): void {
  const seenFields = new Set<string>();
  for (const fields of Object.values(i18nLabelGroups)) {
    for (const field of fields) {
      if (seenFields.has(field)) {
        throw new Error(`Field "${field}" is duplicated in label groups.`);
      }
      seenFields.add(field);
    }
  }
}

// Extract form fields from schema builders
function extractFormFields(formBuilderPath: string): string[] {
  const content = fs.readFileSync(formBuilderPath, 'utf-8');
  const sourceFile = ts.createSourceFile(
    formBuilderPath,
    content,
    ts.ScriptTarget.Latest,
    true
  );

  const keys: string[] = [];
  function extractNestedFields(typeNode: ts.TypeNode, prefix: string) {
    if (!ts.isTypeLiteralNode(typeNode)) return;

    for (const member of typeNode.members) {
      if (
        !member.name ||
        (!ts.isIdentifier(member.name) && !ts.isStringLiteral(member.name))
      )
        continue;

      const propKey = ts.isIdentifier(member.name)
        ? member.name.text
        : member.name.text;
      const fullKey = `${prefix}-${propKey}`;

      if (ts.isPropertySignature(member) && member.type) {
        if (ts.isTypeReferenceNode(member.type)) {
          const typeName = member.type.typeName.getText();

          if (typeName === 'FormControl') {
            keys.push(fullKey);
          } else if (
            typeName === 'FormArray' &&
            member.type.typeArguments?.[0]
          ) {
            const innerType = member.type.typeArguments[0];

            if (
              ts.isTypeReferenceNode(innerType) &&
              innerType.typeName.getText() === 'FormControl'
            ) {
              keys.push(fullKey);
            } else if (
              ts.isTypeReferenceNode(innerType) &&
              innerType.typeName.getText() === 'FormGroup' &&
              innerType.typeArguments?.[0] &&
              ts.isTypeLiteralNode(innerType.typeArguments[0])
            ) {
              extractNestedFields(innerType.typeArguments[0], fullKey);
            } else if (ts.isTypeLiteralNode(innerType)) {
              extractNestedFields(innerType, fullKey);
            }
          } else if (
            typeName === 'FormGroup' &&
            member.type.typeArguments?.[0]
          ) {
            const groupLiteral = member.type.typeArguments[0];
            if (ts.isTypeLiteralNode(groupLiteral)) {
              extractNestedFields(groupLiteral, fullKey);
            }
          }
        }
      }
    }
  }

  function visit(node: ts.Node) {
    if (
      ts.isFunctionDeclaration(node) &&
      node.name?.text === 'getFormSchema' &&
      node.body
    ) {
      for (const stmt of node.body.statements) {
        if (
          ts.isReturnStatement(stmt) &&
          stmt.expression &&
          ts.isObjectLiteralExpression(stmt.expression)
        ) {
          for (const prop of stmt.expression.properties) {
            if (!ts.isPropertyAssignment(prop)) {
              throw new Error(
                'Expected property assignment in getFormSchema return object.'
              );
            }

            const key = ts.isStringLiteral(prop.name)
              ? prop.name.text
              : undefined;
            if (!key) {
              throw new Error(
                'Expected string literal property name in getFormSchema return object.'
              );
            }

            const initializer = prop.initializer;

            if (ts.isCallExpression(initializer)) {
              const expr = initializer.expression;
              const typeArgs = initializer.typeArguments;

              const fnName = ts.isPropertyAccessExpression(expr)
                ? expr.name.text
                : undefined;

              if (fnName === 'control') {
                keys.push(key);
              } else if (
                (fnName === 'array' || fnName === 'group') &&
                typeArgs?.[0]
              ) {
                const typeArg = typeArgs[0];

                if (
                  fnName === 'array' &&
                  ts.isTypeReferenceNode(typeArg) &&
                  typeArg.typeName.getText() === 'FormControl'
                ) {
                  keys.push(key);
                } else if (ts.isTypeLiteralNode(typeArg)) {
                  extractNestedFields(typeArg, key);
                } else if (
                  ts.isTypeReferenceNode(typeArg) &&
                  typeArg.typeName.getText() === 'FormGroup' &&
                  typeArg.typeArguments?.[0] &&
                  ts.isTypeLiteralNode(typeArg.typeArguments[0])
                ) {
                  extractNestedFields(typeArg.typeArguments[0], key);
                }
              }
            }
          }
        }
      }
    }

    ts.forEachChild(node, visit);
  }

  visit(sourceFile);
  return keys;
}

function getLocalizedString(id: string, content: string): string {
  return `$localize\`:@@${id}:${content}\``;
}

function getPrefixedRuleId(ruleId: string): string {
  return `rule.${ruleId}`;
}

function getLocalizedErrorMessage(
  ruleId: string,
  ruleDescription: string
): string {
  return getLocalizedString(getPrefixedRuleId(ruleId), ruleDescription);
}

function splitPascalCaseLabel(label: string): string {
  const parts = splitPascalCase(label);
  return parts.join(' ');
}

function getFormMetadata(i18nLabelGroups: Record<string, string[]>): {
  formMetadata: FormMetadata;
  errorMessageTranslationProposals: Record<string, string>;
} {
  const metadata: FormMetadata = {};
  const errorMessageTranslationProposals: Record<string, string> = {};

  for (const config of FORM_CONFIGS) {
    const formFields = extractFormFields(config.schemaPath);
    const formSetupEntry = formSetup[config.pageKey];
    const formSetupVisibleFields: Record<string, string> =
      formSetupEntry.visible;
    const formSetupServerOnlyFields: Record<string, string> =
      formSetupEntry.serverOnly;
    const validationRules = filterRules(clientRules, formFields);

    // Generate error message keys for i18n
    const errorMessages = new Map<string, string>();
    for (const rule of validationRules) {
      if (rule.type === 'condition') {
        const nestedRules = rule.rules;
        for (const nestedRule of nestedRules) {
          errorMessages.set(
            nestedRule.id,
            getLocalizedErrorMessage(nestedRule.id, nestedRule.description)
          );
          errorMessageTranslationProposals[getPrefixedRuleId(nestedRule.id)] =
            nestedRule.descriptionTranslationProposal;
        }
      } else {
        errorMessages.set(
          rule.id,
          getLocalizedErrorMessage(rule.id, rule.description)
        );
        errorMessageTranslationProposals[getPrefixedRuleId(rule.id)] =
          rule.descriptionTranslationProposal;
      }
    }

    const labels: Record<string, string> = {};
    for (const field of formFields) {
      // Check if the field has a shared label in i18nLabelGroups
      const sharedLabel = Object.entries(i18nLabelGroups).find(([_, fields]) =>
        fields.includes(field)
      );
      if (sharedLabel) {
        labels[field] = getLocalizedString(
          `label.${sharedLabel[0]}`,
          splitPascalCaseLabel(sharedLabel[0])
        );
      } else {
        // If no shared label, use the field name as the key and get the label from the form setup.
        let label: string;
        if (field in formSetupVisibleFields) {
          label = formSetupVisibleFields[field];
        } else if (field in formSetupServerOnlyFields) {
          label = formSetupServerOnlyFields[field];
        } else {
          throw new Error(
            `Field "${field}" is not defined in form setup for page "${config.pageKey}".`
          );
        }

        labels[field] = getLocalizedString(
          `label.${field}`,
          splitPascalCaseLabel(label)
        );
      }
    }

    // Remove 'description' fields from validation rules to reduce file size
    const validationRulesWithoutDescription = validationRules.map((rule) => {
      const ruleWithoutDescription: Rule<string, undefined> =
        rule.type !== 'condition'
          ? {
              ...rule,
              description: undefined,
              descriptionTranslationProposal: undefined,
            }
          : {
              ...rule,
              description: undefined,
              descriptionTranslationProposal: undefined,
              rules: rule.rules.map((nestedRule) => ({
                ...nestedRule,
                description: undefined,
                descriptionTranslationProposal: undefined,
              })),
            };

      return ruleWithoutDescription;
    });

    metadata[config.pageKey] = {
      formFields,
      validationRules: validationRulesWithoutDescription,
      errorMessages: Object.fromEntries(errorMessages),
      labels,
    };
  }

  return { formMetadata: metadata, errorMessageTranslationProposals };
}

// Helper function to generate the metadata object with proper $localize calls
function generateMetadataObject(obj: any, indent = 2): string {
  const spaces = ' '.repeat(indent);

  if (obj === null) {
    return 'null';
  }

  if (obj === undefined) {
    return 'undefined';
  }

  if (typeof obj === 'string') {
    if (obj.startsWith('$localize`')) {
      // Extract the content between the first and last backtick
      const match = obj.match(/^\$localize`([\s\S]*)`$/);
      if (match) {
        const rawContent = match[1];
        // Escape only the *inside* content
        const escapedContent = rawContent
          .replace(/\\/g, '\\\\') // Escape backslashes first (must be first!)
          .replace(/`/g, '\\`') // Escape backticks
          .replace(/[{}]/g, (match) => (match === '{' ? '${"{"}' : '${"}"}')); // Curly braces are interpreted as expressions in $localize, so we need to surround them with ${'{'} and ${'{'}

        return `$localize\`${escapedContent}\``; // Wrap in real backticks
      }
      // Fallback if no match — maybe malformed, return as-is
      return obj;
    }

    return JSON.stringify(obj);
  }

  if (typeof obj === 'number' || typeof obj === 'boolean') {
    return String(obj);
  }

  if (Array.isArray(obj)) {
    const items = obj.map((item) => generateMetadataObject(item, indent + 2));
    return `[\n${spaces}  ${items.join(`,\n${spaces}  `)}\n${spaces}]`;
  }

  if (typeof obj === 'object') {
    const entries = Object.entries(obj).map(([key, value]) => {
      const formattedValue = generateMetadataObject(value, indent + 2);
      return `${spaces}  ${JSON.stringify(key)}: ${formattedValue}`;
    });

    return `{\n${entries.join(',\n')}\n${spaces}}`;
  }

  return JSON.stringify(obj);
}

function generateFormMetadataTypeScriptFile(metadata: FormMetadata): string {
  const metadataString = generateMetadataObject(metadata);

  return `// This file is auto-generated. Do not edit manually.
import { FormMetadata } from "@shared/types"

export const FORM_METADATA: FormMetadata = ${metadataString};

export function getErrorMessages(pageKey: string): Map<string, string> {
  const errorMessages = FORM_METADATA[pageKey]?.errorMessages;
  return errorMessages ? new Map(Object.entries(errorMessages)) : new Map();
}
`;
}

function generateErrorMessageTranslationProposalsTypeScriptFile(
  translationProposals: Record<string, string>
): string {
  const translationProposalsString =
    generateMetadataObject(translationProposals);

  return `// This file is auto-generated. Do not edit manually.
export const ERROR_MESSAGE_TRANSLATION_PROPOSALS: Record<string, string> = ${translationProposalsString};
`;
}

function generateLabelGroupingProposal(): Record<string, string[]> {
  const labelGroupingProposal: Map<string, string[]> = new Map();

  for (const fields of Object.values(formSetup)) {
    const visibleFields = fields.visible;
    const serverOnlyFields = fields.serverOnly;
    const allFields = { ...visibleFields, ...serverOnlyFields };
    for (const [fieldName, label] of Object.entries(allFields)) {
      if (!labelGroupingProposal.has(label)) {
        labelGroupingProposal.set(label, []);
      }
      labelGroupingProposal.get(label)?.push(fieldName);
    }
  }

  // Only print out labels that have more than one field associated with them
  for (const [label, fields] of labelGroupingProposal.entries()) {
    if (fields.length <= 1) {
      labelGroupingProposal.delete(label);
    } else {
      // Sort fields for consistency
      fields.sort();
    }
  }

  return Object.fromEntries(labelGroupingProposal);
}

function generateFormMetadata() {
  // Make sure the 'formSetup' from the lib matches the form schemas defined in the client.
  validateFormSetupMatchesSchemas();

  // Generate a proposal for the grouping of labels. All labels with the same name in the original JSON schema are grouped together.
  const labelGroupingProposal = generateLabelGroupingProposal();

  const i18nLabelGroupsContent = fs.readFileSync(
    path.join(__dirname, './input/i18n-label-groups.json'),
    'utf8'
  );
  const parsedI18nLabelGroupsContent = JSON.parse(i18nLabelGroupsContent);
  let i18nLabelGroups: Record<string, string[]> = {};
  try {
    i18nLabelGroups =
      i18nLabelGroupsSchema.parse(parsedI18nLabelGroupsContent)
        .i18nLabelGroups || {};
  } catch (error) {
    console.error('⚠️ Error parsing i18nLabelGroups:', error);
  }
  validateLabelGroupsMatchFormSetup(i18nLabelGroups);
  validateLabelGroupsDoNotContainDuplicates(i18nLabelGroups);

  // Generate the metadata and write to file
  const metadata = getFormMetadata(i18nLabelGroups);
  const generatedFormMetadataContent = generateFormMetadataTypeScriptFile(
    metadata.formMetadata
  );
  const generatedErrorMessageTranslationProposalsContent =
    generateErrorMessageTranslationProposalsTypeScriptFile(
      metadata.errorMessageTranslationProposals
    );

  const outputPath = path.join(__dirname, '../src/app/generated');
  fs.mkdirSync(path.dirname(outputPath), { recursive: true });
  fs.writeFileSync(
    `${outputPath}/pacs008-form-metadata.ts`,
    generatedFormMetadataContent
  );
  fs.writeFileSync(
    `${outputPath}/pacs008-error-message-translation-proposals.ts`,
    generatedErrorMessageTranslationProposalsContent
  );
  fs.writeFileSync(
    `${outputPath}/pacs008-label-grouping-proposal.json`,
    JSON.stringify(labelGroupingProposal, null, 2)
  );

  console.log('Form metadata generated successfully!');
}

generateFormMetadata();
