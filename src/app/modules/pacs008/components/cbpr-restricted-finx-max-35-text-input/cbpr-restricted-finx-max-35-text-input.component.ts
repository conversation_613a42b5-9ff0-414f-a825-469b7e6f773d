import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { IMBFormTextInputComponent } from '@shared/ui';

@Component({
  selector: 'app-cbpr-restricted-finx-max-35-text-input',
  imports: [IMBFormTextInputComponent],
  templateUrl: './cbpr-restricted-finx-max-35-text-input.component.html',
  styleUrl: './cbpr-restricted-finx-max-35-text-input.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CBPR_RestrictedFINXMax35TextInputComponent {
  fieldName = input.required<string>();
  label = input.required<string>();
  isReadOnly = input.required<boolean>();
}
