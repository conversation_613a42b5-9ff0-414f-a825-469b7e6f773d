import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { FieldErrorsComponent } from '@helaba/iso20022-lib/components';
import { TagModule } from 'primeng/tag';

@Component({
  selector: 'app-form-field-errors',
  imports: [TagModule, FieldErrorsComponent],
  templateUrl: './form-field-errors.component.html',
  styleUrl: './form-field-errors.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FormFieldErrorsComponent {
  fieldName = input.required<string>();
  fieldId = input.required<string>();
}
