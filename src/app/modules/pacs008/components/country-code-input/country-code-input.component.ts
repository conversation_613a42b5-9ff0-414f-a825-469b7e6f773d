import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { IMBFormTextInputComponent } from '@shared/ui';

@Component({
  selector: 'app-country-code-input',
  imports: [IMBFormTextInputComponent],
  templateUrl: './country-code-input.component.html',
  styleUrl: './country-code-input.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CountryCodeInputComponent {
  fieldName = input.required<string>();
  label = input.required<string>();
  isReadOnly = input.required<boolean>();
}
