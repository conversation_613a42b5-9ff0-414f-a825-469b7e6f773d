import {
  ChangeDetectionStrategy,
  Component,
  computed,
  input,
  output,
  Type,
} from '@angular/core';
import {
  ControlContainer,
  FormGroupDirective,
  ReactiveFormsModule,
} from '@angular/forms';
import { FormFieldErrorsComponent } from '../../form-field-errors';
import { PlainFieldsetComponent } from '../../form-structures';
import { IMBFormValueComponent } from '../../imb-form-value';
import { InputNumberInputEvent, InputNumberModule } from 'primeng/inputnumber';
import { FormSingleInputComponent } from '@helaba/iso20022-lib/components';

@Component({
  selector: 'app-imb-form-number-input',
  imports: [
    ReactiveFormsModule,
    FormSingleInputComponent,
    InputNumberModule,
    FormFieldErrorsComponent,
    IMBFormValueComponent,
  ],
  templateUrl: './imb-form-number-input.component.html',
  styleUrl: './imb-form-number-input.component.scss',
  viewProviders: [
    { provide: ControlContainer, useExisting: FormGroupDirective }, // When resolving 'ControlContainer', just use the parent form that was declared up the tree, required to use 'formControlName' within the component.
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class IMBFormNumberInputComponent {
  label = input.required<string>();
  fieldName = input.required<string>();
  isReadOnly = input.required<boolean>();
  fieldPrefix = input<string>('');

  max = input<number | null>(null);
  maxFractionDigits = input<number>(0);
  onChange = output<number | null>();

  fieldsetComponent: Type<PlainFieldsetComponent> = PlainFieldsetComponent;

  onInput($event: InputNumberInputEvent) {
    const value = $event.value;
    this.onChange.emit(
      value === null
        ? null
        : typeof value === 'string'
        ? parseFloat(value)
        : value
    );
  }
}
