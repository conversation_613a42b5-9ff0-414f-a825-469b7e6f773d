import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { IMBFormTextInputComponent } from '@shared/ui';

@Component({
  selector: 'app-external-person-identification-1-code-input',
  imports: [IMBFormTextInputComponent],
  templateUrl: './external-person-identification-1-code-input.component.html',
  styleUrl: './external-person-identification-1-code-input.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ExternalPersonIdentification1CodeInputComponent {
  fieldName = input.required<string>();
  label = input.required<string>();
  isReadOnly = input.required<boolean>();
}
