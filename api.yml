openapi: 3.1.0
info:
  title: OpenAPI definition
  version: v0
servers:
- url: http://localhost:8080
  description: Generated server url
paths:
  /api/input/{type}:
    post:
      tags:
      - message-controller
      summary: Generate XML Message based on MessageType and request payload
      operationId: genXmlMessage
      parameters:
      - name: type
        in: path
        required: true
        schema:
          type: string
          enum:
          - pacs_008_001_08
      requestBody:
        content:
          application/json:
            schema:
              type: object
              additionalProperties: {}
        required: true
      responses:
        "200":
          description: XML message generated successfully
          content:
            application/xml: {}
        "400":
          description: Validation failed
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Result"
  /api/input/{type}/upload:
    post:
      tags:
      - message-controller
      operationId: uploadXml
      parameters:
      - name: type
        in: path
        required: true
        schema:
          type: string
          enum:
          - pacs_008_001_08
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
              required:
              - file
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: object
                additionalProperties: {}
  /api/input/createNested:
    post:
      tags:
      - message-controller
      operationId: createNestedJson
      requestBody:
        content:
          application/json:
            schema:
              type: object
              additionalProperties: {}
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
  /api/search:
    get:
      tags:
      - search-controller
      operationId: query
      parameters:
      - name: key
        in: query
        required: true
        schema:
          type: string
      - name: query
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  type: string
components:
  schemas:
    Result:
      type: object
      properties:
        rules:
          $ref: "#/components/schemas/ValidationResult"
        data:
          $ref: "#/components/schemas/ValidationResult"
        schema:
          $ref: "#/components/schemas/ValidationResult"
        successful:
          type: boolean
    ValidationResult:
      type: object
      properties:
        successful:
          type: boolean
        validationErrors:
          type: array
          items:
            type: string
