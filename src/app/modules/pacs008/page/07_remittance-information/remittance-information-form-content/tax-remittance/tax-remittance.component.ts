import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { HiddenSectionComponent, IMBFormTextInputComponent } from '@shared/ui';
import {
  CBPR_RestrictedFINXMax140Text_ExtendedInputComponent,
  CBPR_RestrictedFINXMax35Text_ExtendedInputComponent,
  ISODateInputComponent,
  ActiveOrHistoricCurrencyAndAmountInputComponent,
  TaxParty2__1InputComponent,
  TaxPeriod2InputComponent,
} from '../../../../components';

@Component({
  selector: 'app-tax-remittance',
  imports: [
    CBPR_RestrictedFINXMax140Text_ExtendedInputComponent,
    HiddenSectionComponent,
    IMBFormTextInputComponent,
    CBPR_RestrictedFINXMax35Text_ExtendedInputComponent,
    ISODateInputComponent,
    ActiveOrHistoricCurrencyAndAmountInputComponent,
    TaxParty2__1InputComponent,
    TaxPeriod2InputComponent,
  ],
  templateUrl: './tax-remittance.component.html',
  styleUrl: './tax-remittance.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TaxRemittanceComponent {
  isReadOnly = input.required<boolean>();
  labels = input.required<Record<string, string>>();
}
