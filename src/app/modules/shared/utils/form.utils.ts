export function flattenArrays(
  obj: Record<string, unknown>,
  fieldPrefix: string = ''
): Record<string, string> {
  let transformedObj: Record<string, string> = {};
  for (const [key, value] of Object.entries(obj)) {
    const mergedPrefix = `${fieldPrefix}${fieldPrefix ? '-' : ''}${key}`;
    if (typeof value === 'string') {
      transformedObj[mergedPrefix] = value;
    } else if (Array.isArray(value)) {
      if (value.length === 0) {
        transformedObj[mergedPrefix] = '';
      }
      for (const [index, entry] of value.entries()) {
        if (typeof entry === 'string') {
          transformedObj[`${mergedPrefix}-#${index}`] = entry;
        } else if (
          typeof entry === 'object' &&
          entry !== null &&
          !Array.isArray(entry)
        ) {
          transformedObj = {
            ...transformedObj,
            ...flattenArrays(entry, `${mergedPrefix}-#${index}`),
          };
        } else {
          throw new Error(
            `Expected a string or an object for key ${key}, but got ${typeof entry}`
          );
        }
      }
    } else {
      throw new Error(
        `Expected a string or an array for key ${key}, but got ${typeof value}`
      );
    }
  }

  return transformedObj;
}
