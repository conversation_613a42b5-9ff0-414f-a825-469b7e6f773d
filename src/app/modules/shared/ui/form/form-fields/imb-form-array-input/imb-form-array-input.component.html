<app-form-array-input
  [label]="label()"
  [fieldName]="fieldName()"
  [isReadOnly]="isReadOnly()"
  [fieldPrefix]="fieldPrefix()"
  [fieldTemplate]="fieldTemplate"
  [errorTemplate]="errorTemplate"
  [formValueFieldTemplate]="formValueFieldTemplate"
  [addItemButtonTemplate]="addItemButtonTemplate"
  [removeItemButtonTemplate]="removeItemButtonTemplate"
  [fieldsetComponent]="fieldsetComponent"
/>

<ng-template
  #fieldTemplate
  let-fieldId
  let-index="index"
  let-formGroup="formGroup"
>
  <div [formGroup]="formGroup">
    <input
      type="text"
      pInputText
      [id]="fieldId"
      [formControlName]="index"
      class="formArrayInput"
    />
  </div>
</ng-template>

<ng-template #errorTemplate let-fieldId>
  <app-form-field-errors [fieldName]="fieldName()" [fieldId]="fieldId" />
</ng-template>

<ng-template
  #formValueFieldTemplate
  let-fieldName
  let-formControl="formControl"
>
  <app-imb-form-value [fieldName]="fieldName" [formControl]="formControl" />
</ng-template>

<ng-template #addItemButtonTemplate let-addItem>
  <p-button icon="pi pi-plus" (click)="addItem()" />
</ng-template>

<ng-template #removeItemButtonTemplate let-removeItem let-index="index">
  <p-button
    icon="pi pi-times"
    [rounded]="true"
    [text]="true"
    severity="danger"
    (click)="removeItem(index)"
  />
</ng-template>
