import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import {
  HiddenSectionComponent,
  IMBFormTextInputComponent,
  OneOfSelectorComponent,
} from '@shared/ui';
import { CBPR_RestrictedFINXMax35TextInputComponent } from '../cbpr-restricted-finx-max-35-text-input';
import { ActiveOrHistoricCurrencyCodeInputComponent } from '../active-or-historic-currency-code-input';

@Component({
  selector: 'app-cash-account-38-1-input',
  imports: [
    HiddenSectionComponent,
    CBPR_RestrictedFINXMax35TextInputComponent,
    OneOfSelectorComponent,
    IMBFormTextInputComponent,
    ActiveOrHistoricCurrencyCodeInputComponent,
  ],
  templateUrl: './cash-account-38-1-input.component.html',
  styleUrl: './cash-account-38-1-input.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CashAccount38__1InputComponent {
  ibanFieldName = input.required<string>();
  ibanLabel = input.required<string>();
  idFieldName = input.required<string>();
  idLabel = input.required<string>();
  identificationSchemeNameCodeFieldName = input.required<string>();
  identificationSchemeNameCodeLabel = input.required<string>();
  proprietaryIdentificationSchemeNameFieldName = input.required<string>();
  proprietaryIdentificationSchemeNameLabel = input.required<string>();
  identificationIssuerFieldName = input.required<string>();
  identificationIssuerLabel = input.required<string>();
  typeCodeFieldName = input.required<string>();
  typeCodeLabel = input.required<string>();
  proprietaryTypeFieldName = input.required<string>();
  proprietaryTypeLabel = input.required<string>();
  currencyFieldName = input.required<string>();
  currencyLabel = input.required<string>();
  nameFieldName = input.required<string>();
  nameLabel = input.required<string>();
  proxyTypeCodeFieldName = input.required<string>();
  proxyTypeCodeLabel = input.required<string>();
  proprietaryProxyTypeFieldName = input.required<string>();
  proprietaryProxyTypeLabel = input.required<string>();
  proxyIdFieldName = input.required<string>();
  proxyIdLabel = input.required<string>();

  isReadOnly = input.required<boolean>();
}
