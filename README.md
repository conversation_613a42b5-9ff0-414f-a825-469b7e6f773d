# ISO 20022 Message Builder

Install dependencies using `npm i`.

## Key Scripts

### Form Metadata Generation

To enable i18n extraction for the error messages, we need to write them out during build. This way, `$localize` can statically examine the error messages.
This step also allows us to precompute other data (e.g. Which rules are relevant for the respective page?) required for the different form pages, improving runtime performance. A metadata file is created at `src/app/generated/pacs008-form-metadata.ts` including localized error messages. This script automatically runs before `start` and `build` commands.

```bash
npm run generate-form-metadata
```

### Internationalization (i18n)

```bash
# Extract translatable strings to XLF file and merge new strings into target language files
npm run update-i18n
```

The workflow:

1. Run `npm run generate-form-metadata` to prepare the localized error messages in case the lib was updated.
2. Run `npm run update-i18n`.
3. Translations are stored in `src/assets/i18n/` as XLF files
4. Look for "TODO" entries in the `messages.de.xlf` file and enter translations.
5. (Optional) Apply translation proposals automatically where no translation has been provided so far (`<target>` contains `TODO: translate`): `npm run dev:apply-translation-proposals`
6. (Optional) Apply translation proposals automatically wherever the translation proposal differs from the current `<target>` text (**careful, this erases possibly hand-crafted translations**): `npm run dev:apply-translation-proposals -- --overwrite-targets`
7. Run `npm run update-i18n` again to ensure proper formatting.

### Run app in development

```bash
npm run start
```

## Git Export/Import Scripts

Scripts for transferring Git changes between environments when only text content can be copied.

## Usage

### Exporting Changes

```bash
# Export only uncommitted changes
npm run internal:export-import-changes

# Export last 2 commits + uncommitted changes
npm run internal:export-import-changes -- 2
```

Creates `git-changes-export.json` in project root.

### Importing Changes

1. Copy content from `git-changes-export.json`
2. Paste into `git-changes-export.json` in target environment
3. Run: `npm run internal:export-import-changes`

### Features

- Preserves commit messages, timestamps, and order
- Handles file creation, modification, and deletion
- Validates timestamps to prevent importing old changes
- Creates directory structures automatically
- Blocks import if uncommitted changes exist
