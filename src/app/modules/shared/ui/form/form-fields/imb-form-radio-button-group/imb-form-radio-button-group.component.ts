import { ChangeDetectionStrategy, Component, input, Type } from '@angular/core';
import {
  ControlContainer,
  FormGroupDirective,
  ReactiveFormsModule,
} from '@angular/forms';
import { InputTextModule } from 'primeng/inputtext';
import { FormRadioButtonGroupComponent } from '@helaba/iso20022-lib/components';
import { FormFieldErrorsComponent } from '../../form-field-errors';
import { SelectOption } from '@helaba/iso20022-lib/util';
import { PlainFieldsetComponent } from '../../form-structures';
import { IMBFormValueComponent } from '../../imb-form-value';

@Component({
  selector: 'app-imb-form-radio-button-group',
  imports: [
    ReactiveFormsModule,
    InputTextModule,
    FormFieldErrorsComponent,
    IMBFormValueComponent,
    FormRadioButtonGroupComponent,
  ],
  templateUrl: './imb-form-radio-button-group.component.html',
  styleUrl: './imb-form-radio-button-group.component.scss',
  viewProviders: [
    { provide: ControlContainer, useExisting: FormGroupDirective }, // When resolving 'ControlContainer', just use the parent form that was declared up the tree, required to use 'formControlName' within the component.
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class IMBFormRadioButtonGroupComponent<T extends string> {
  label = input.required<string>();
  fieldName = input.required<string>();
  isReadOnly = input.required<boolean>();
  fieldPrefix = input<string>('');
  options = input.required<SelectOption<T>[]>();

  fieldsetComponent: Type<PlainFieldsetComponent> = PlainFieldsetComponent;
}
