.formValue {
  --p-inputtext-border-color: var(--p-stone-50);
  --p-inputtext-padding-x: 0.5rem;
  --p-inputtext-padding-y: 0.2rem;

  width: 100%;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;

  &:focus {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    --p-inputtext-border-color: var(--p-blue-300);
  }

  // Smaller inputs for nested levels
  :host([data-level="2"]) &,
  :host([data-level="3"]) &,
  :host([data-level="4"]) & {
    --p-inputtext-padding-x: 0.625rem;
    --p-inputtext-padding-y: 0.375rem;
    font-size: 0.875rem;
  }
}
