<div class="oneOfSelectorContainer">
  <p-fieldset [legend]="header()" class="oneOfSelectorFieldset">
    <div class="oneOfSelectButton">
      <app-select-button
        [options]="selectButtonOptions()"
        [isReadOnly]="isReadOnly()"
        [defaultValue]="
          isReadOnly()
            ? hasFilledOutOption1Fields()
              ? OPTION1
              : hasFilledOutOption2Fields()
              ? OPTION2
              : null
            : null
        "
        (onChange)="onChangeSelectedOption($event)"
      />
    </div>

    <div class="oneOfContent">
      @if (selectedOption() === OPTION1) {
      <p-panel>
        <ng-content select="[slot='option1']" />
      </p-panel>
      } @if (selectedOption() === OPTION2) {
      <p-panel>
        <ng-content select="[slot='option2']" />
      </p-panel>
      } @if (isReadOnly() && !hasFilledOutOption1Fields() &&
      !hasFilledOutOption2Fields()) {
      <p-panel> No option selected. </p-panel>
      }
    </div>

    <!--  Only show errors if no option is selected. If an option is selected, the individual fields are shown, displaying their own errors. -->
    @if (!selectedOption()) {
    <div>
      @for (localFieldId of allFieldIds(); track localFieldId) {
      <app-form-field-errors
        [fieldName]="localFieldId"
        [fieldId]="getGlobalFieldId(localFieldId)"
      />
      }
    </div>
    }
  </p-fieldset>
</div>
