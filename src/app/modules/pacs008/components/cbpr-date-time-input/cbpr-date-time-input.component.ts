import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { IMBFormDateInputComponent } from '@shared/ui';

@Component({
  selector: 'app-cbpr-date-time-input',
  imports: [IMBFormDateInputComponent],
  templateUrl: './cbpr-date-time-input.component.html',
  styleUrl: './cbpr-date-time-input.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CBPR_DateTimeInputComponent {
  fieldName = input.required<string>();
  label = input.required<string>();
  isReadOnly = input.required<boolean>();
}
