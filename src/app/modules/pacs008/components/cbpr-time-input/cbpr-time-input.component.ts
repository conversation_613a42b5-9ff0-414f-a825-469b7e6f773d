import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { IMBFormDateInputComponent } from '@shared/ui';

@Component({
  selector: 'app-cbpr-time-input',
  imports: [IMBFormDateInputComponent],
  templateUrl: './cbpr-time-input.component.html',
  styleUrl: './cbpr-time-input.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CBPR_TimeInputComponent {
  fieldName = input.required<string>();
  label = input.required<string>();
  isReadOnly = input.required<boolean>();
}
