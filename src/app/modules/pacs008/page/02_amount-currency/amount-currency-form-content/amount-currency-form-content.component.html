<app-hidden-section
  header="Instructed Amount"
  i18n-header
  [errorScopes]="['FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAmt']"
>
  <app-cbpr-amount-1-input
    amountFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAmt-amount"
    [amountLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAmt-amount']"
    currencyFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAmt-Ccy"
    [currencyLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAmt-Ccy']"
    [isReadOnly]="isReadOnly()"
  />
</app-hidden-section>
<app-hidden-section
  header="Interbank Settlement Amount"
  i18n-header
  [errorScopes]="['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrBkSttlmAmt']"
>
  <app-cbpr-amount-1-input
    amountFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrBkSttlmAmt-amount"
    [amountLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrBkSttlmAmt-amount']
    "
    currencyFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrBkSttlmAmt-Ccy"
    [currencyLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrBkSttlmAmt-Ccy']
    "
    [isReadOnly]="isReadOnly()"
  />
</app-hidden-section>
<app-imb-form-text-input
  fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-XchgRate"
  [label]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-XchgRate']"
  [isReadOnly]="isReadOnly()"
/>
<app-imb-form-radio-button-group
  [label]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgBr']"
  fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgBr"
  [options]="chargeBearerOptions"
  [isReadOnly]="isReadOnly()"
/>
<app-hidden-section
  header="Charges Information"
  i18n-header
  [errorScopes]="['FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf']"
>
  <!-- <app-form-group-array label="" /> -->

  <app-hidden-section
    header="Financial Institution Identification"
    i18n-header
    [errorScopes]="['FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId']"
  >
    <app-branch-and-financial-institution-identification-6-1-input
      bicfiFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-BICFI"
      [bicfiLabel]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-BICFI']
      "
      clearingSystemIdCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"
      [clearingSystemIdCodeLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId-ClrSysId-Cd'
        ]
      "
      clearingSystemMemberIdFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId-MmbId"
      [clearingSystemMemberIdLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId-MmbId'
        ]
      "
      leiIdentifierFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-LEI"
      [leiIdentifierLabel]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-LEI']
      "
      nameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-Nm"
      [nameLabel]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-Nm']
      "
      departmentFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Dept"
      [departmentLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Dept'
        ]
      "
      subDepartmentFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-SubDept"
      [subDepartmentLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-SubDept'
        ]
      "
      streetNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-StrtNm"
      [streetNameLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-StrtNm'
        ]
      "
      buildingNumberFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-BldgNb"
      [buildingNumberLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-BldgNb'
        ]
      "
      buildingNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-BldgNm"
      [buildingNameLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-BldgNm'
        ]
      "
      floorFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Flr"
      [floorLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Flr'
        ]
      "
      postBoxFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-PstBx"
      [postBoxLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-PstBx'
        ]
      "
      roomFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Room"
      [roomLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Room'
        ]
      "
      postCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-PstCd"
      [postCodeLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-PstCd'
        ]
      "
      townNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-TwnNm"
      [townNameLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-TwnNm'
        ]
      "
      townLocationNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-TwnLctnNm"
      [townLocationNameLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-TwnLctnNm'
        ]
      "
      districtNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-DstrctNm"
      [districtNameLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-DstrctNm'
        ]
      "
      countrySubdivisionFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-CtrySubDvsn"
      [countrySubdivisionLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-CtrySubDvsn'
        ]
      "
      countryFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Ctry"
      [countryLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Ctry'
        ]
      "
      addressLineFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-AdrLine"
      [addressLineLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-AdrLine'
        ]
      "
      [isReadOnly]="isReadOnly()"
    />
  </app-hidden-section>
  <app-cbpr-amount-1-input
    amountFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Amt-amount"
    [amountLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Amt-amount']
    "
    currencyFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Amt-Ccy"
    [currencyLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Amt-Ccy']"
    [isReadOnly]="isReadOnly()"
  />
</app-hidden-section>
