// This file is auto-generated. Do not edit manually.
export const ERROR_MESSAGE_TRANSLATION_PROPOSALS: Record<string, string> = {
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-InstrId__required": "CdtTrfTxInf-PmtId-InstrId ist erforderlich.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-InstrId__maxLength": "CdtTrfTxInf-PmtId-InstrId darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-InstrId__pattern": "CdtTrfTxInf-PmtId-InstrId muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-EndToEndId__required": "CdtTrfTxInf-PmtId-EndToEndId ist erforderlich.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-EndToEndId__maxLength": "CdtTrfTxInf-PmtId-EndToEndId darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-EndToEndId__pattern": "CdtTrfTxInf-PmtId-EndToEndId muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-TxId__maxLength": "CdtTrfTxInf-PmtId-TxId darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-TxId__pattern": "CdtTrfTxInf-PmtId-TxId muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-InstrPrty__pattern": "CdtTrfTxInf-PmtTpInf-InstrPrty muss einer der folgenden Werte sein: HIGH, NORM.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-ClrChanl__pattern": "CdtTrfTxInf-PmtTpInf-ClrChanl muss einer der folgenden Werte sein: RTGS, RTNS, MPNS, BOOK.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl__maxItems": "CdtTrfTxInf-PmtTpInf-SvcLvl darf höchstens 3 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl-Cd__maxLength": "CdtTrfTxInf-PmtTpInf-SvcLvl-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl-Prtry__maxLength": "CdtTrfTxInf-PmtTpInf-SvcLvl-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl-Prtry__pattern": "CdtTrfTxInf-PmtTpInf-SvcLvl-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-LclInstrm-Cd__maxLength": "CdtTrfTxInf-PmtTpInf-LclInstrm-Cd darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-LclInstrm-Prtry__maxLength": "CdtTrfTxInf-PmtTpInf-LclInstrm-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-LclInstrm-Prtry__pattern": "CdtTrfTxInf-PmtTpInf-LclInstrm-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-CtgyPurp-Cd__maxLength": "CdtTrfTxInf-PmtTpInf-CtgyPurp-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-CtgyPurp-Prtry__maxLength": "CdtTrfTxInf-PmtTpInf-CtgyPurp-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-CtgyPurp-Prtry__pattern": "CdtTrfTxInf-PmtTpInf-CtgyPurp-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Purp-Cd__maxLength": "CdtTrfTxInf-Purp-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Purp-Prtry__maxLength": "CdtTrfTxInf-Purp-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Purp-Prtry__pattern": "CdtTrfTxInf-Purp-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrBkSttlmAmt-Ccy__pattern": "CdtTrfTxInf-IntrBkSttlmAmt-Ccy muss dem Muster ^[A-Z]{3,3}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrBkSttlmAmt-Ccy__required": "CdtTrfTxInf-IntrBkSttlmAmt-Ccy ist erforderlich.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrBkSttlmAmt-amount__maxLength": "CdtTrfTxInf-IntrBkSttlmAmt-amount darf höchstens 15 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrBkSttlmAmt-amount__pattern": "CdtTrfTxInf-IntrBkSttlmAmt-amount muss dem Muster ^0*(([0-9]{0,9}\\.[0-9]{1,5})|([0-9]{0,10}\\.[0-9]{1,4})|([0-9]{0,11}\\.[0-9]{1,3})|([0-9]{0,12}\\.[0-9]{1,2})|([0-9]{0,13}\\.[0-9]{1,1})|([0-9]{0,18}\\.)|0*|([0-9]{0,14}))$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrBkSttlmAmt-amount__required": "CdtTrfTxInf-IntrBkSttlmAmt-amount ist erforderlich.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAmt-Ccy__pattern": "CdtTrfTxInf-InstdAmt-Ccy muss dem Muster ^[A-Z]{3,3}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAmt-amount__maxLength": "CdtTrfTxInf-InstdAmt-amount darf höchstens 15 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAmt-amount__pattern": "CdtTrfTxInf-InstdAmt-amount muss dem Muster ^0*(([0-9]{0,9}\\.[0-9]{1,5})|([0-9]{0,10}\\.[0-9]{1,4})|([0-9]{0,11}\\.[0-9]{1,3})|([0-9]{0,12}\\.[0-9]{1,2})|([0-9]{0,13}\\.[0-9]{1,1})|([0-9]{0,18}\\.)|0*|([0-9]{0,14}))$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-XchgRate__maxLength": "CdtTrfTxInf-XchgRate darf höchstens 12 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgBr__pattern": "CdtTrfTxInf-ChrgBr muss einer der folgenden Werte sein: DEBT, CRED, SHAR.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgBr__required": "CdtTrfTxInf-ChrgBr ist erforderlich.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Amt-Ccy__pattern": "CdtTrfTxInf-ChrgsInf-Amt-Ccy muss dem Muster ^[A-Z]{3,3}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Amt-amount__maxLength": "CdtTrfTxInf-ChrgsInf-Amt-amount darf höchstens 15 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Amt-amount__pattern": "CdtTrfTxInf-ChrgsInf-Amt-amount muss dem Muster ^0*(([0-9]{0,9}\\.[0-9]{1,5})|([0-9]{0,10}\\.[0-9]{1,4})|([0-9]{0,11}\\.[0-9]{1,3})|([0-9]{0,12}\\.[0-9]{1,2})|([0-9]{0,13}\\.[0-9]{1,1})|([0-9]{0,18}\\.)|0*|([0-9]{0,14}))$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-BICFI__pattern": "CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-BICFI muss dem Muster ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId-ClrSysId-Cd__maxLength": "CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId-ClrSysId-Cd darf höchstens 5 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId-MmbId__maxLength": "CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId-MmbId darf höchstens 28 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId-MmbId__pattern": "CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId-MmbId muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-LEI__pattern": "CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-LEI muss dem Muster ^[A-Z0-9]{18,18}[0-9]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-Nm__maxLength": "CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-Nm darf höchstens 140 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-Nm__pattern": "CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-Nm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Dept__maxLength": "CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Dept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Dept__pattern": "CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Dept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-SubDept__maxLength": "CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-SubDept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-SubDept__pattern": "CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-SubDept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-StrtNm__maxLength": "CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-StrtNm darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-StrtNm__pattern": "CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-StrtNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-BldgNb__maxLength": "CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-BldgNb darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-BldgNb__pattern": "CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-BldgNb muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-BldgNm__maxLength": "CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-BldgNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-BldgNm__pattern": "CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-BldgNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Flr__maxLength": "CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Flr darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Flr__pattern": "CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Flr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-PstBx__maxLength": "CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-PstBx darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-PstBx__pattern": "CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-PstBx muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Room__maxLength": "CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Room darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Room__pattern": "CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Room muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-PstCd__maxLength": "CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-PstCd darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-PstCd__pattern": "CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-PstCd muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-TwnNm__maxLength": "CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-TwnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-TwnNm__pattern": "CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-TwnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-TwnLctnNm__maxLength": "CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-TwnLctnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-TwnLctnNm__pattern": "CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-TwnLctnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-DstrctNm__maxLength": "CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-DstrctNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-DstrctNm__pattern": "CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-DstrctNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-CtrySubDvsn__maxLength": "CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-CtrySubDvsn darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-CtrySubDvsn__pattern": "CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-CtrySubDvsn muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Ctry__pattern": "CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Ctry muss dem Muster ^[A-Z]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-AdrLine__maxItems": "CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-AdrLine darf höchstens 3 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-AdrLine__maxLength": "CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-AdrLine darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-AdrLine__pattern": "CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-AdrLine muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAmt-Ccy__conditional-required__18da5fb5+FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAmt-Ccy__required-conditional": "CdtTrfTxInf-InstdAmt-Ccy ist erforderlich, wenn CdtTrfTxInf-InstdAmt-amount vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAmt-amount__conditional-required__46fa7be0+FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAmt-amount__required-conditional": "CdtTrfTxInf-InstdAmt-amount ist erforderlich, wenn CdtTrfTxInf-InstdAmt-Ccy vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Amt-Ccy__conditional-required__bdb7990+FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Amt-Ccy__required-conditional": "CdtTrfTxInf-ChrgsInf-Amt-Ccy ist erforderlich, wenn CdtTrfTxInf-ChrgsInf-Amt-amount vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Amt-amount__conditional-required__9b2bd165+FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Amt-amount__required-conditional": "CdtTrfTxInf-ChrgsInf-Amt-amount ist erforderlich, wenn CdtTrfTxInf-ChrgsInf-Amt-Ccy vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId-ClrSysId__conditional-required__1ed1d390+FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId-ClrSysId-Cd__required-conditional": "CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId-ClrSysId-Cd ist erforderlich, wenn CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId-MmbId vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId-MmbId__conditional-required__8168f03c+FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId-MmbId__required-conditional": "CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId-MmbId ist erforderlich, wenn CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId-ClrSysId-Cd vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Amt__conditional-required__a5704f01+FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Amt-Ccy__required-conditional": "CdtTrfTxInf-ChrgsInf-Amt-Ccy ist erforderlich, wenn CdtTrfTxInf-ChrgsInf-Agt-FinInstnId vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Amt__conditional-required__a5704f01+FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Amt-amount__required-conditional": "CdtTrfTxInf-ChrgsInf-Amt-amount ist erforderlich, wenn CdtTrfTxInf-ChrgsInf-Agt-FinInstnId vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmMtd__pattern": "GrpHdr-SttlmInf-SttlmMtd muss einer der folgenden Werte sein: INDA, INGA, COVE.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmMtd__required": "GrpHdr-SttlmInf-SttlmMtd ist erforderlich.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-IBAN__pattern": "GrpHdr-SttlmInf-SttlmAcct-Id-IBAN muss dem Muster ^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id__maxLength": "GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id darf höchstens 34 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id__pattern": "GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id muss dem Muster ^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Cd__maxLength": "GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Prtry__maxLength": "GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Prtry__pattern": "GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Issr__maxLength": "GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Issr darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Issr__pattern": "GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Issr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Cd__maxLength": "GrpHdr-SttlmInf-SttlmAcct-Tp-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Prtry__maxLength": "GrpHdr-SttlmInf-SttlmAcct-Tp-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Prtry__pattern": "GrpHdr-SttlmInf-SttlmAcct-Tp-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Ccy__pattern": "GrpHdr-SttlmInf-SttlmAcct-Ccy muss dem Muster ^[A-Z]{3,3}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Nm__maxLength": "GrpHdr-SttlmInf-SttlmAcct-Nm darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Nm__pattern": "GrpHdr-SttlmInf-SttlmAcct-Nm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Cd__maxLength": "GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Prtry__maxLength": "GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Prtry__pattern": "GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Id__maxLength": "GrpHdr-SttlmInf-SttlmAcct-Prxy-Id darf höchstens 320 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Id__pattern": "GrpHdr-SttlmInf-SttlmAcct-Prxy-Id muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI__pattern": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI muss dem Muster ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd__maxLength": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd darf höchstens 5 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId__maxLength": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId darf höchstens 28 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId__pattern": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-LEI__pattern": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-LEI muss dem Muster ^[A-Z0-9]{18,18}[0-9]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm__maxLength": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm darf höchstens 140 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm__pattern": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Dept__maxLength": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Dept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Dept__pattern": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Dept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-SubDept__maxLength": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-SubDept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-SubDept__pattern": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-SubDept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm__maxLength": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm__pattern": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb__maxLength": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb__pattern": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm__maxLength": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm__pattern": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Flr__maxLength": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Flr darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Flr__pattern": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Flr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstBx__maxLength": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstBx darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstBx__pattern": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstBx muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Room__maxLength": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Room darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Room__pattern": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Room muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstCd__maxLength": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstCd darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstCd__pattern": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstCd muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm__maxLength": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm__pattern": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm__maxLength": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm__pattern": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm__maxLength": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm__pattern": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn__maxLength": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn__pattern": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__pattern": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry muss dem Muster ^[A-Z]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__maxItems": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine darf höchstens 3 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__maxLength": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__pattern": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-IBAN__pattern": "GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-IBAN muss dem Muster ^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-Id__maxLength": "GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-Id darf höchstens 34 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-Id__pattern": "GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-Id muss dem Muster ^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-SchmeNm-Cd__maxLength": "GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry__maxLength": "GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry__pattern": "GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-Issr__maxLength": "GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-Issr darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-Issr__pattern": "GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-Issr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Tp-Cd__maxLength": "GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Tp-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Tp-Prtry__maxLength": "GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Tp-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Tp-Prtry__pattern": "GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Tp-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Ccy__pattern": "GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Ccy muss dem Muster ^[A-Z]{3,3}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Nm__maxLength": "GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Nm darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Nm__pattern": "GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Nm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Tp-Cd__maxLength": "GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Tp-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Tp-Prtry__maxLength": "GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Tp-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Tp-Prtry__pattern": "GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Tp-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Id__maxLength": "GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Id darf höchstens 320 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Id__pattern": "GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Id muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI__pattern": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI muss dem Muster ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd__maxLength": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd darf höchstens 5 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId__maxLength": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId darf höchstens 28 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId__pattern": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-LEI__pattern": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-LEI muss dem Muster ^[A-Z0-9]{18,18}[0-9]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm__maxLength": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm darf höchstens 140 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm__pattern": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Dept__maxLength": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Dept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Dept__pattern": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Dept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept__maxLength": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept__pattern": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm__maxLength": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm__pattern": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb__maxLength": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb__pattern": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm__maxLength": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm__pattern": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Flr__maxLength": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Flr darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Flr__pattern": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Flr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx__maxLength": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx__pattern": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Room__maxLength": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Room darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Room__pattern": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Room muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd__maxLength": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd__pattern": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm__maxLength": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm__pattern": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm__maxLength": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm__pattern": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm__maxLength": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm__pattern": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn__maxLength": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn__pattern": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__pattern": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry muss dem Muster ^[A-Z]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__maxItems": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine darf höchstens 3 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__maxLength": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__pattern": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-IBAN__pattern": "GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-IBAN muss dem Muster ^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-Id__maxLength": "GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-Id darf höchstens 34 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-Id__pattern": "GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-Id muss dem Muster ^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Cd__maxLength": "GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry__maxLength": "GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry__pattern": "GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-Issr__maxLength": "GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-Issr darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-Issr__pattern": "GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-Issr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Tp-Cd__maxLength": "GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Tp-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Tp-Prtry__maxLength": "GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Tp-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Tp-Prtry__pattern": "GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Tp-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Ccy__pattern": "GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Ccy muss dem Muster ^[A-Z]{3,3}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Nm__maxLength": "GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Nm darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Nm__pattern": "GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Nm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Tp-Cd__maxLength": "GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Tp-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Tp-Prtry__maxLength": "GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Tp-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Tp-Prtry__pattern": "GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Tp-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Id__maxLength": "GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Id darf höchstens 320 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Id__pattern": "GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Id muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI__pattern": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI muss dem Muster ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd__maxLength": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd darf höchstens 5 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId__maxLength": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId darf höchstens 28 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId__pattern": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-LEI__pattern": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-LEI muss dem Muster ^[A-Z0-9]{18,18}[0-9]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm__maxLength": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm darf höchstens 140 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm__pattern": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Dept__maxLength": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Dept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Dept__pattern": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Dept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept__maxLength": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept__pattern": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm__maxLength": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm__pattern": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb__maxLength": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb__pattern": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm__maxLength": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm__pattern": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Flr__maxLength": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Flr darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Flr__pattern": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Flr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx__maxLength": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx__pattern": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Room__maxLength": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Room darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Room__pattern": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Room muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd__maxLength": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd__pattern": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm__maxLength": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm__pattern": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm__maxLength": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm__pattern": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm__maxLength": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm__pattern": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn__maxLength": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn__pattern": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__pattern": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry muss dem Muster ^[A-Z]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__maxItems": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine darf höchstens 3 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__maxLength": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__pattern": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-IBAN__pattern": "GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-IBAN muss dem Muster ^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-Id__maxLength": "GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-Id darf höchstens 34 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-Id__pattern": "GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-Id muss dem Muster ^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Cd__maxLength": "GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry__maxLength": "GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry__pattern": "GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-Issr__maxLength": "GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-Issr darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-Issr__pattern": "GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-Issr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Tp-Cd__maxLength": "GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Tp-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Tp-Prtry__maxLength": "GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Tp-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Tp-Prtry__pattern": "GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Tp-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Ccy__pattern": "GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Ccy muss dem Muster ^[A-Z]{3,3}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Nm__maxLength": "GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Nm darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Nm__pattern": "GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Nm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Tp-Cd__maxLength": "GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Tp-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Tp-Prtry__maxLength": "GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Tp-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Tp-Prtry__pattern": "GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Tp-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Id__maxLength": "GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Id darf höchstens 320 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Id__pattern": "GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Id muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrBkSttlmDt__pattern": "CdtTrfTxInf-IntrBkSttlmDt muss dem Muster ^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)(?:Z|[+-][01]\\d:[0-5]\\d)?$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrBkSttlmDt__required": "CdtTrfTxInf-IntrBkSttlmDt ist erforderlich.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmPrty__pattern": "CdtTrfTxInf-SttlmPrty muss einer der folgenden Werte sein: URGT, HIGH, NORM.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmIndctn-DbtDtTm__pattern": "CdtTrfTxInf-SttlmTmIndctn-DbtDtTm muss dem Muster ^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)T(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d(?:\\.[0-9]+)?(?:Z|[+-][01]\\d:[0-5]\\d)?$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmIndctn-CdtDtTm__pattern": "CdtTrfTxInf-SttlmTmIndctn-CdtDtTm muss dem Muster ^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)T(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d(?:\\.[0-9]+)?(?:Z|[+-][01]\\d:[0-5]\\d)?$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmReq-CLSTm__pattern": "CdtTrfTxInf-SttlmTmReq-CLSTm muss dem Muster ^(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d((?:\\.[0-9]+)?)?(?:Z|[+-][01]\\d:[0-5]\\d)?$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmReq-TillTm__pattern": "CdtTrfTxInf-SttlmTmReq-TillTm muss dem Muster ^(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d((?:\\.[0-9]+)?)?(?:Z|[+-][01]\\d:[0-5]\\d)?$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmReq-FrTm__pattern": "CdtTrfTxInf-SttlmTmReq-FrTm muss dem Muster ^(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d((?:\\.[0-9]+)?)?(?:Z|[+-][01]\\d:[0-5]\\d)?$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmReq-RjctTm__pattern": "CdtTrfTxInf-SttlmTmReq-RjctTm muss dem Muster ^(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d((?:\\.[0-9]+)?)?(?:Z|[+-][01]\\d:[0-5]\\d)?$ entsprechen.",
    "rule.R23__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__contains": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine darf Daten, die in FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnNm oder TwnLctnNm oder DstrctNm oder CtrySubDvsn oder Ctry) vorhanden sind, nicht wiederholen. (R23)",
    "rule.R23__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__contains": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine darf Daten, die in FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnNm oder TwnLctnNm oder DstrctNm oder CtrySubDvsn oder Ctry) vorhanden sind, nicht wiederholen. (R23)",
    "rule.R23__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__contains": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine darf Daten, die in FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnNm oder TwnLctnNm oder DstrctNm oder CtrySubDvsn oder Ctry) vorhanden sind, nicht wiederholen. (R23)",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id__conditional-required__5a25c994+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id__required-conditional": "GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id ist erforderlich, wenn mindestens eines der Felder GrpHdr-SttlmInf-SttlmAcct-Id-Othr-(SchmeNm oder Issr) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Id__conditional-required__e9120c83+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Id__required-conditional": "GrpHdr-SttlmInf-SttlmAcct-Prxy-Id ist erforderlich, wenn GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id__conditional-required__3f1d6460+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-IBAN__required-conditional": "GrpHdr-SttlmInf-SttlmAcct-Id-IBAN ist erforderlich, wenn mindestens eines der Felder GrpHdr-SttlmInf-SttlmAcct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id__conditional-required__3f1d6460+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id__required-conditional": "GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id ist erforderlich, wenn mindestens eines der Felder GrpHdr-SttlmInf-SttlmAcct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId__conditional-required__ca9e3526+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd__required-conditional": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd ist erforderlich, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId__conditional-required__34f7b0ca+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId__required-conditional": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId ist erforderlich, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-Id__conditional-required__d5ac1e38+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-Id__required-conditional": "GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-Id ist erforderlich, wenn mindestens eines der Felder GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-(SchmeNm oder Issr) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Id__conditional-required__d3686083+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Id__required-conditional": "GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Id ist erforderlich, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Tp vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id__conditional-required__ee7a0f0c+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-IBAN__required-conditional": "GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-IBAN ist erforderlich, wenn mindestens eines der Felder GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id__conditional-required__ee7a0f0c+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-Id__required-conditional": "GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-Id ist erforderlich, wenn mindestens eines der Felder GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId__conditional-required__47825e25+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd__required-conditional": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd ist erforderlich, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId__conditional-required__92c97dc9+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId__required-conditional": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId ist erforderlich, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-Id__conditional-required__679afcdb+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-Id__required-conditional": "GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-Id ist erforderlich, wenn mindestens eines der Felder GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-(SchmeNm oder Issr) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Id__conditional-required__f3a5c083+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Id__required-conditional": "GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Id ist erforderlich, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Tp vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id__conditional-required__e997652f+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-IBAN__required-conditional": "GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-IBAN ist erforderlich, wenn mindestens eines der Felder GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id__conditional-required__e997652f+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-Id__required-conditional": "GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-Id ist erforderlich, wenn mindestens eines der Felder GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId__conditional-required__ad6202eb+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd__required-conditional": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd ist erforderlich, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId__conditional-required__778e9587+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId__required-conditional": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId ist erforderlich, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-Id__conditional-required__49177d55+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-Id__required-conditional": "GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-Id ist erforderlich, wenn mindestens eines der Felder GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-(SchmeNm oder Issr) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Id__conditional-required__af4da9e3+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Id__required-conditional": "GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Id ist erforderlich, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Tp vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id__conditional-required__a15cce1+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-IBAN__required-conditional": "GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-IBAN ist erforderlich, wenn mindestens eines der Felder GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id__conditional-required__a15cce1+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-Id__required-conditional": "GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-Id ist erforderlich, wenn mindestens eines der Felder GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__2bd65408+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm__prohibited-conditional": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(Nm und ClrSysMmbId und PstlAdr) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__2bd65408+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd__prohibited-conditional": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(ClrSysMmbId und Nm und PstlAdr) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__2bd65408+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId__prohibited-conditional": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(ClrSysMmbId und Nm und PstlAdr) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__2bd65408+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Dept__prohibited-conditional": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__2bd65408+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-SubDept__prohibited-conditional": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__2bd65408+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm__prohibited-conditional": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__2bd65408+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb__prohibited-conditional": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__2bd65408+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm__prohibited-conditional": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__2bd65408+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Flr__prohibited-conditional": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__2bd65408+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstBx__prohibited-conditional": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__2bd65408+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Room__prohibited-conditional": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__2bd65408+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstCd__prohibited-conditional": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__2bd65408+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm__prohibited-conditional": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__2bd65408+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm__prohibited-conditional": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__2bd65408+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm__prohibited-conditional": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__2bd65408+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn__prohibited-conditional": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__2bd65408+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__prohibited-conditional": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__2bd65408+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__prohibited-conditional": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__4625bcb+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm__prohibited-conditional": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(Nm und ClrSysMmbId und PstlAdr) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__4625bcb+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd__prohibited-conditional": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(ClrSysMmbId und Nm und PstlAdr) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__4625bcb+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId__prohibited-conditional": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(ClrSysMmbId und Nm und PstlAdr) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__4625bcb+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Dept__prohibited-conditional": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__4625bcb+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept__prohibited-conditional": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__4625bcb+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm__prohibited-conditional": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__4625bcb+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb__prohibited-conditional": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__4625bcb+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm__prohibited-conditional": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__4625bcb+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Flr__prohibited-conditional": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__4625bcb+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx__prohibited-conditional": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__4625bcb+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Room__prohibited-conditional": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__4625bcb+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd__prohibited-conditional": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__4625bcb+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm__prohibited-conditional": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__4625bcb+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm__prohibited-conditional": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__4625bcb+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm__prohibited-conditional": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__4625bcb+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn__prohibited-conditional": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__4625bcb+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__prohibited-conditional": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__4625bcb+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__prohibited-conditional": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__6426f05+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm__prohibited-conditional": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(Nm und ClrSysMmbId und PstlAdr) ist nicht erlaubt, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__6426f05+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd__prohibited-conditional": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(ClrSysMmbId und Nm und PstlAdr) ist nicht erlaubt, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__6426f05+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId__prohibited-conditional": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(ClrSysMmbId und Nm und PstlAdr) ist nicht erlaubt, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__6426f05+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Dept__prohibited-conditional": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__6426f05+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept__prohibited-conditional": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__6426f05+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm__prohibited-conditional": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__6426f05+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb__prohibited-conditional": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__6426f05+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm__prohibited-conditional": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__6426f05+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Flr__prohibited-conditional": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__6426f05+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx__prohibited-conditional": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__6426f05+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Room__prohibited-conditional": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__6426f05+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd__prohibited-conditional": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__6426f05+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm__prohibited-conditional": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__6426f05+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm__prohibited-conditional": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__6426f05+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm__prohibited-conditional": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__6426f05+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn__prohibited-conditional": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__6426f05+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__prohibited-conditional": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__6426f05+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__prohibited-conditional": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)",
    "rule.R17-R18__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI__conditional-prohibited__12ab2446+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI__prohibited-conditional": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI ist nicht erlaubt, wenn mindestens eines der Felder GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(ClrSysMmbId oder PstlAdr oder Nm) vorhanden ist. (R17, R18)",
    "rule.R17-R18__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI__conditional-prohibited__fc1f47e6+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI__prohibited-conditional": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI ist nicht erlaubt, wenn mindestens eines der Felder GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(ClrSysMmbId oder PstlAdr oder Nm) vorhanden ist. (R17, R18)",
    "rule.R17-R18__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI__conditional-prohibited__8f9a7886+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI__prohibited-conditional": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI ist nicht erlaubt, wenn mindestens eines der Felder GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(ClrSysMmbId oder PstlAdr oder Nm) vorhanden ist. (R17, R18)",
    "rule.R17-R18__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__conditional-required__23f906a0+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__required-conditional": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry ist erforderlich, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm gesetzt ist. (R17, R18)",
    "rule.R17-R18__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-[AdrLine_TwnNm]__conditional-required__981175ec+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__required-conditional": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(AdrLine oder TwnNm) ist erforderlich, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm gesetzt ist. (R17, R18)",
    "rule.R17-R18__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-[AdrLine_TwnNm]__conditional-required__981175ec+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm__required-conditional": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(TwnNm oder AdrLine) ist erforderlich, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm gesetzt ist. (R17, R18)",
    "rule.R17-R18__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__conditional-required__cc574323+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__required-conditional": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry ist erforderlich, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm gesetzt ist. (R17, R18)",
    "rule.R17-R18__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-[AdrLine_TwnNm]__conditional-required__d792e44f+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__required-conditional": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(AdrLine oder TwnNm) ist erforderlich, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm gesetzt ist. (R17, R18)",
    "rule.R17-R18__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-[AdrLine_TwnNm]__conditional-required__d792e44f+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm__required-conditional": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(TwnNm oder AdrLine) ist erforderlich, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm gesetzt ist. (R17, R18)",
    "rule.R17-R18__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__conditional-required__75b0636d+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__required-conditional": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry ist erforderlich, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm gesetzt ist. (R17, R18)",
    "rule.R17-R18__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-[AdrLine_TwnNm]__conditional-required__5c7c5041+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__required-conditional": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(AdrLine oder TwnNm) ist erforderlich, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm gesetzt ist. (R17, R18)",
    "rule.R17-R18__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-[AdrLine_TwnNm]__conditional-required__5c7c5041+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm__required-conditional": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(TwnNm oder AdrLine) ist erforderlich, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm gesetzt ist. (R17, R18)",
    "rule.R17-R18__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm__conditional-required__e5151dbf+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm__required-conditional": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm ist erforderlich, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr vorhanden ist. (R17, R18)",
    "rule.R17-R18__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm__conditional-required__50f58b9c+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm__required-conditional": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm ist erforderlich, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr vorhanden ist. (R17, R18)",
    "rule.R17-R18__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm__conditional-required__6866b32+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm__required-conditional": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm ist erforderlich, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr vorhanden ist. (R17, R18)",
    "rule.R20-R25-R29__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__conditional-required__23f906a0+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__required-conditional": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry ist erforderlich, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm gesetzt ist. (R20, R25, R29)",
    "rule.R20-R25-R29__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-[AdrLine_TwnNm]__conditional-required__981175ec+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__required-conditional": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(AdrLine oder TwnNm) ist erforderlich, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm gesetzt ist. (R20, R25, R29)",
    "rule.R20-R25-R29__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-[AdrLine_TwnNm]__conditional-required__981175ec+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm__required-conditional": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(TwnNm oder AdrLine) ist erforderlich, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm gesetzt ist. (R20, R25, R29)",
    "rule.R20-R25-R29__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__conditional-required__cc574323+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__required-conditional": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry ist erforderlich, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm gesetzt ist. (R20, R25, R29)",
    "rule.R20-R25-R29__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-[AdrLine_TwnNm]__conditional-required__d792e44f+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__required-conditional": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(AdrLine oder TwnNm) ist erforderlich, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm gesetzt ist. (R20, R25, R29)",
    "rule.R20-R25-R29__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-[AdrLine_TwnNm]__conditional-required__d792e44f+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm__required-conditional": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(TwnNm oder AdrLine) ist erforderlich, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm gesetzt ist. (R20, R25, R29)",
    "rule.R20-R25-R29__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__conditional-required__75b0636d+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__required-conditional": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry ist erforderlich, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm gesetzt ist. (R20, R25, R29)",
    "rule.R20-R25-R29__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-[AdrLine_TwnNm]__conditional-required__5c7c5041+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__required-conditional": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(AdrLine oder TwnNm) ist erforderlich, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm gesetzt ist. (R20, R25, R29)",
    "rule.R20-R25-R29__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-[AdrLine_TwnNm]__conditional-required__5c7c5041+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm__required-conditional": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(TwnNm oder AdrLine) ist erforderlich, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm gesetzt ist. (R20, R25, R29)",
    "rule.R20-R25-R29__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm__conditional-required__e5151dbf+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm__required-conditional": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm ist erforderlich, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr vorhanden ist. (R20, R25, R29)",
    "rule.R20-R25-R29__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm__conditional-required__50f58b9c+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm__required-conditional": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm ist erforderlich, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr vorhanden ist. (R20, R25, R29)",
    "rule.R20-R25-R29__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm__conditional-required__6866b32+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm__required-conditional": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm ist erforderlich, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr vorhanden ist. (R20, R25, R29)",
    "rule.R22-R27-R31__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-[TwnNm_Ctry]__conditional-required__e92a3b8d+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm__required-conditional": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(TwnNm und Ctry) ist erforderlich, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine gesetzt ist and mindestens eines der Felder GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnLctnNm oder DstrctNm oder CtrySubDvsn) vorhanden ist. (R22, R27, R31)",
    "rule.R22-R27-R31__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-[TwnNm_Ctry]__conditional-required__e92a3b8d+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__required-conditional": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(Ctry und TwnNm) ist erforderlich, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine gesetzt ist and mindestens eines der Felder GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnLctnNm oder DstrctNm oder CtrySubDvsn) vorhanden ist. (R22, R27, R31)",
    "rule.R22-R27-R31__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-[TwnNm_Ctry]__conditional-required__e0cb6bee+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm__required-conditional": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(TwnNm und Ctry) ist erforderlich, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine gesetzt ist and mindestens eines der Felder GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnLctnNm oder DstrctNm oder CtrySubDvsn) vorhanden ist. (R22, R27, R31)",
    "rule.R22-R27-R31__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-[TwnNm_Ctry]__conditional-required__e0cb6bee+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__required-conditional": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(Ctry und TwnNm) ist erforderlich, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine gesetzt ist and mindestens eines der Felder GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnLctnNm oder DstrctNm oder CtrySubDvsn) vorhanden ist. (R22, R27, R31)",
    "rule.R22-R27-R31__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-[TwnNm_Ctry]__conditional-required__7c3a0c20+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm__required-conditional": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(TwnNm und Ctry) ist erforderlich, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine gesetzt ist and mindestens eines der Felder GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnLctnNm oder DstrctNm oder CtrySubDvsn) vorhanden ist. (R22, R27, R31)",
    "rule.R22-R27-R31__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-[TwnNm_Ctry]__conditional-required__7c3a0c20+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__required-conditional": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(Ctry und TwnNm) ist erforderlich, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine gesetzt ist and mindestens eines der Felder GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnLctnNm oder DstrctNm oder CtrySubDvsn) vorhanden ist. (R22, R27, R31)",
    "rule.R22-R27-R31__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__conditional-maxItems__e92a3b8d+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__maxItems-conditional": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine darf höchstens 2 Elemente haben, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine gesetzt ist and mindestens eines der Felder GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnLctnNm oder DstrctNm oder CtrySubDvsn) vorhanden ist. (R22, R27, R31)",
    "rule.R22-R27-R31__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__conditional-maxItems__e0cb6bee+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__maxItems-conditional": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine darf höchstens 2 Elemente haben, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine gesetzt ist and mindestens eines der Felder GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnLctnNm oder DstrctNm oder CtrySubDvsn) vorhanden ist. (R22, R27, R31)",
    "rule.R22-R27-R31__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__conditional-maxItems__7c3a0c20+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__maxItems-conditional": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine darf höchstens 2 Elemente haben, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine gesetzt ist and mindestens eines der Felder GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnLctnNm oder DstrctNm oder CtrySubDvsn) vorhanden ist. (R22, R27, R31)",
    "rule.R24-R28-R32__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__conditional-maxLength__a2be53f1+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__maxLength-conditional": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine darf höchstens 35 Zeichen lang sein, wenn keines der Felder GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(Dept und SubDept und StrtNm und BldgNb und BldgNm und Flr und PstBx und Room und PstCd und TwnNm und TwnLctnNm und DstrctNm und CtrySubDvsn und Ctry) vorhanden ist. (R24, R28, R32)",
    "rule.R24-R28-R32__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__conditional-maxLength__17579e71+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__maxLength-conditional": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine darf höchstens 35 Zeichen lang sein, wenn keines der Felder GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(Dept und SubDept und StrtNm und BldgNb und BldgNm und Flr und PstBx und Room und PstCd und TwnNm und TwnLctnNm und DstrctNm und CtrySubDvsn und Ctry) vorhanden ist. (R24, R28, R32)",
    "rule.R24-R28-R32__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__conditional-maxLength__2ae6dcd1+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__maxLength-conditional": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine darf höchstens 35 Zeichen lang sein, wenn keines der Felder GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(Dept und SubDept und StrtNm und BldgNb und BldgNm und Flr und PstBx und Room und PstCd und TwnNm und TwnLctnNm und DstrctNm und CtrySubDvsn und Ctry) vorhanden ist. (R24, R28, R32)",
    "rule.ThirdReimbursementAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-[BICFI_Nm]__conditional-required__babd48c1+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI__required-conditional": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(BICFI oder Nm) ist erforderlich, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId vorhanden ist. (ThirdReimbursementAgentRule)",
    "rule.ThirdReimbursementAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-[BICFI_Nm]__conditional-required__babd48c1+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm__required-conditional": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(Nm oder BICFI) ist erforderlich, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId vorhanden ist. (ThirdReimbursementAgentRule)",
    "rule.ThirdReimbursementAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-[BICFI_Nm]__conditional-required__babd48c1+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI__required-conditional": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(BICFI oder Nm) ist erforderlich, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId vorhanden ist. (ThirdReimbursementAgentRule)",
    "rule.ThirdReimbursementAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-[BICFI_Nm]__conditional-required__babd48c1+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm__required-conditional": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(Nm oder BICFI) ist erforderlich, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId vorhanden ist. (ThirdReimbursementAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI__prohibited-conditional": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt und InstdRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd__prohibited-conditional": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt und InstdRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId__prohibited-conditional": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt und InstdRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-LEI__prohibited-conditional": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt und InstdRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm__prohibited-conditional": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt und InstdRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Dept__prohibited-conditional": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt und InstdRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-SubDept__prohibited-conditional": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt und InstdRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm__prohibited-conditional": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt und InstdRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb__prohibited-conditional": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt und InstdRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm__prohibited-conditional": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt und InstdRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Flr__prohibited-conditional": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt und InstdRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstBx__prohibited-conditional": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt und InstdRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Room__prohibited-conditional": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt und InstdRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstCd__prohibited-conditional": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt und InstdRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm__prohibited-conditional": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt und InstdRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm__prohibited-conditional": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt und InstdRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm__prohibited-conditional": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt und InstdRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn__prohibited-conditional": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt und InstdRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__prohibited-conditional": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt und InstdRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__prohibited-conditional": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt und InstdRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI__prohibited-conditional": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt und InstgRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd__prohibited-conditional": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt und InstgRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId__prohibited-conditional": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt und InstgRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-LEI__prohibited-conditional": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt und InstgRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm__prohibited-conditional": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt und InstgRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Dept__prohibited-conditional": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt und InstgRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept__prohibited-conditional": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt und InstgRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm__prohibited-conditional": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt und InstgRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb__prohibited-conditional": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt und InstgRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm__prohibited-conditional": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt und InstgRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Flr__prohibited-conditional": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt und InstgRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx__prohibited-conditional": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt und InstgRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Room__prohibited-conditional": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt und InstgRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd__prohibited-conditional": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt und InstgRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm__prohibited-conditional": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt und InstgRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm__prohibited-conditional": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt und InstgRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm__prohibited-conditional": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt und InstgRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn__prohibited-conditional": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt und InstgRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__prohibited-conditional": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt und InstgRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__prohibited-conditional": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt und InstgRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI__prohibited-conditional": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt und InstgRmbrsmntAgt und InstdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd__prohibited-conditional": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt und InstgRmbrsmntAgt und InstdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId__prohibited-conditional": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt und InstgRmbrsmntAgt und InstdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-LEI__prohibited-conditional": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt und InstgRmbrsmntAgt und InstdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm__prohibited-conditional": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt und InstgRmbrsmntAgt und InstdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Dept__prohibited-conditional": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt und InstgRmbrsmntAgt und InstdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept__prohibited-conditional": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt und InstgRmbrsmntAgt und InstdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm__prohibited-conditional": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt und InstgRmbrsmntAgt und InstdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb__prohibited-conditional": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt und InstgRmbrsmntAgt und InstdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm__prohibited-conditional": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt und InstgRmbrsmntAgt und InstdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Flr__prohibited-conditional": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt und InstgRmbrsmntAgt und InstdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx__prohibited-conditional": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt und InstgRmbrsmntAgt und InstdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Room__prohibited-conditional": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt und InstgRmbrsmntAgt und InstdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd__prohibited-conditional": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt und InstgRmbrsmntAgt und InstdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm__prohibited-conditional": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt und InstgRmbrsmntAgt und InstdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm__prohibited-conditional": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt und InstgRmbrsmntAgt und InstdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm__prohibited-conditional": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt und InstgRmbrsmntAgt und InstdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn__prohibited-conditional": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt und InstgRmbrsmntAgt und InstdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__prohibited-conditional": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt und InstgRmbrsmntAgt und InstdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__prohibited-conditional": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt und InstgRmbrsmntAgt und InstdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)",
    "rule.SettlementMethodCoverRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct__conditional-prohibited__24fcd5dc+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-IBAN__prohibited-conditional": "GrpHdr-SttlmInf-SttlmAcct ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd den Wert \"COVE\" hat. (SettlementMethodCoverRule)",
    "rule.SettlementMethodCoverRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct__conditional-prohibited__24fcd5dc+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id__prohibited-conditional": "GrpHdr-SttlmInf-SttlmAcct ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd den Wert \"COVE\" hat. (SettlementMethodCoverRule)",
    "rule.SettlementMethodCoverRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct__conditional-prohibited__24fcd5dc+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Cd__prohibited-conditional": "GrpHdr-SttlmInf-SttlmAcct ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd den Wert \"COVE\" hat. (SettlementMethodCoverRule)",
    "rule.SettlementMethodCoverRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct__conditional-prohibited__24fcd5dc+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Prtry__prohibited-conditional": "GrpHdr-SttlmInf-SttlmAcct ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd den Wert \"COVE\" hat. (SettlementMethodCoverRule)",
    "rule.SettlementMethodCoverRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct__conditional-prohibited__24fcd5dc+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Issr__prohibited-conditional": "GrpHdr-SttlmInf-SttlmAcct ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd den Wert \"COVE\" hat. (SettlementMethodCoverRule)",
    "rule.SettlementMethodCoverRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct__conditional-prohibited__24fcd5dc+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Cd__prohibited-conditional": "GrpHdr-SttlmInf-SttlmAcct ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd den Wert \"COVE\" hat. (SettlementMethodCoverRule)",
    "rule.SettlementMethodCoverRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct__conditional-prohibited__24fcd5dc+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Prtry__prohibited-conditional": "GrpHdr-SttlmInf-SttlmAcct ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd den Wert \"COVE\" hat. (SettlementMethodCoverRule)",
    "rule.SettlementMethodCoverRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct__conditional-prohibited__24fcd5dc+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Ccy__prohibited-conditional": "GrpHdr-SttlmInf-SttlmAcct ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd den Wert \"COVE\" hat. (SettlementMethodCoverRule)",
    "rule.SettlementMethodCoverRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct__conditional-prohibited__24fcd5dc+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Nm__prohibited-conditional": "GrpHdr-SttlmInf-SttlmAcct ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd den Wert \"COVE\" hat. (SettlementMethodCoverRule)",
    "rule.SettlementMethodCoverRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct__conditional-prohibited__24fcd5dc+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Cd__prohibited-conditional": "GrpHdr-SttlmInf-SttlmAcct ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd den Wert \"COVE\" hat. (SettlementMethodCoverRule)",
    "rule.SettlementMethodCoverRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct__conditional-prohibited__24fcd5dc+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Prtry__prohibited-conditional": "GrpHdr-SttlmInf-SttlmAcct ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd den Wert \"COVE\" hat. (SettlementMethodCoverRule)",
    "rule.SettlementMethodCoverRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct__conditional-prohibited__24fcd5dc+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Id__prohibited-conditional": "GrpHdr-SttlmInf-SttlmAcct ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd den Wert \"COVE\" hat. (SettlementMethodCoverRule)",
    "rule.SettlementMethodCoverAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstdRmbrsmntAgt-FinInstnId-BICFI_InstdRmbrsmntAgt-FinInstnId-Nm_InstgRmbrsmntAgt-FinInstnId-BICFI_InstgRmbrsmntAgt-FinInstnId-Nm]__conditional-required__24fcd5dc+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI__required-conditional": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt-FinInstnId-BICFI oder InstdRmbrsmntAgt-FinInstnId-Nm oder InstgRmbrsmntAgt-FinInstnId-BICFI oder InstgRmbrsmntAgt-FinInstnId-Nm) ist erforderlich, wenn GrpHdr-SttlmInf-SttlmMtd den Wert \"COVE\" hat. (SettlementMethodCoverAgentRule)",
    "rule.SettlementMethodCoverAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstdRmbrsmntAgt-FinInstnId-BICFI_InstdRmbrsmntAgt-FinInstnId-Nm_InstgRmbrsmntAgt-FinInstnId-BICFI_InstgRmbrsmntAgt-FinInstnId-Nm]__conditional-required__24fcd5dc+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm__required-conditional": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt-FinInstnId-Nm oder InstdRmbrsmntAgt-FinInstnId-BICFI oder InstgRmbrsmntAgt-FinInstnId-BICFI oder InstgRmbrsmntAgt-FinInstnId-Nm) ist erforderlich, wenn GrpHdr-SttlmInf-SttlmMtd den Wert \"COVE\" hat. (SettlementMethodCoverAgentRule)",
    "rule.SettlementMethodCoverAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstdRmbrsmntAgt-FinInstnId-BICFI_InstdRmbrsmntAgt-FinInstnId-Nm_InstgRmbrsmntAgt-FinInstnId-BICFI_InstgRmbrsmntAgt-FinInstnId-Nm]__conditional-required__24fcd5dc+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI__required-conditional": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt-FinInstnId-BICFI oder InstdRmbrsmntAgt-FinInstnId-BICFI oder InstdRmbrsmntAgt-FinInstnId-Nm oder InstgRmbrsmntAgt-FinInstnId-Nm) ist erforderlich, wenn GrpHdr-SttlmInf-SttlmMtd den Wert \"COVE\" hat. (SettlementMethodCoverAgentRule)",
    "rule.SettlementMethodCoverAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstdRmbrsmntAgt-FinInstnId-BICFI_InstdRmbrsmntAgt-FinInstnId-Nm_InstgRmbrsmntAgt-FinInstnId-BICFI_InstgRmbrsmntAgt-FinInstnId-Nm]__conditional-required__24fcd5dc+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm__required-conditional": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt-FinInstnId-Nm oder InstdRmbrsmntAgt-FinInstnId-BICFI oder InstdRmbrsmntAgt-FinInstnId-Nm oder InstgRmbrsmntAgt-FinInstnId-BICFI) ist erforderlich, wenn GrpHdr-SttlmInf-SttlmMtd den Wert \"COVE\" hat. (SettlementMethodCoverAgentRule)",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Nm__maxLength": "CdtTrfTxInf-UltmtDbtr-Nm darf höchstens 140 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Nm__pattern": "CdtTrfTxInf-UltmtDbtr-Nm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Dept__maxLength": "CdtTrfTxInf-UltmtDbtr-PstlAdr-Dept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Dept__pattern": "CdtTrfTxInf-UltmtDbtr-PstlAdr-Dept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-SubDept__maxLength": "CdtTrfTxInf-UltmtDbtr-PstlAdr-SubDept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-SubDept__pattern": "CdtTrfTxInf-UltmtDbtr-PstlAdr-SubDept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-StrtNm__maxLength": "CdtTrfTxInf-UltmtDbtr-PstlAdr-StrtNm darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-StrtNm__pattern": "CdtTrfTxInf-UltmtDbtr-PstlAdr-StrtNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-BldgNb__maxLength": "CdtTrfTxInf-UltmtDbtr-PstlAdr-BldgNb darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-BldgNb__pattern": "CdtTrfTxInf-UltmtDbtr-PstlAdr-BldgNb muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-BldgNm__maxLength": "CdtTrfTxInf-UltmtDbtr-PstlAdr-BldgNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-BldgNm__pattern": "CdtTrfTxInf-UltmtDbtr-PstlAdr-BldgNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Flr__maxLength": "CdtTrfTxInf-UltmtDbtr-PstlAdr-Flr darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Flr__pattern": "CdtTrfTxInf-UltmtDbtr-PstlAdr-Flr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-PstBx__maxLength": "CdtTrfTxInf-UltmtDbtr-PstlAdr-PstBx darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-PstBx__pattern": "CdtTrfTxInf-UltmtDbtr-PstlAdr-PstBx muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Room__maxLength": "CdtTrfTxInf-UltmtDbtr-PstlAdr-Room darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Room__pattern": "CdtTrfTxInf-UltmtDbtr-PstlAdr-Room muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-PstCd__maxLength": "CdtTrfTxInf-UltmtDbtr-PstlAdr-PstCd darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-PstCd__pattern": "CdtTrfTxInf-UltmtDbtr-PstlAdr-PstCd muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-TwnNm__maxLength": "CdtTrfTxInf-UltmtDbtr-PstlAdr-TwnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-TwnNm__pattern": "CdtTrfTxInf-UltmtDbtr-PstlAdr-TwnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-TwnLctnNm__maxLength": "CdtTrfTxInf-UltmtDbtr-PstlAdr-TwnLctnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-TwnLctnNm__pattern": "CdtTrfTxInf-UltmtDbtr-PstlAdr-TwnLctnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-DstrctNm__maxLength": "CdtTrfTxInf-UltmtDbtr-PstlAdr-DstrctNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-DstrctNm__pattern": "CdtTrfTxInf-UltmtDbtr-PstlAdr-DstrctNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-CtrySubDvsn__maxLength": "CdtTrfTxInf-UltmtDbtr-PstlAdr-CtrySubDvsn darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-CtrySubDvsn__pattern": "CdtTrfTxInf-UltmtDbtr-PstlAdr-CtrySubDvsn muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Ctry__pattern": "CdtTrfTxInf-UltmtDbtr-PstlAdr-Ctry muss dem Muster ^[A-Z]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-AdrLine__maxItems": "CdtTrfTxInf-UltmtDbtr-PstlAdr-AdrLine darf höchstens 2 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-AdrLine__maxLength": "CdtTrfTxInf-UltmtDbtr-PstlAdr-AdrLine darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-AdrLine__pattern": "CdtTrfTxInf-UltmtDbtr-PstlAdr-AdrLine muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-AnyBIC__pattern": "CdtTrfTxInf-UltmtDbtr-Id-OrgId-AnyBIC muss dem Muster ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-LEI__pattern": "CdtTrfTxInf-UltmtDbtr-Id-OrgId-LEI muss dem Muster ^[A-Z0-9]{18,18}[0-9]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr__maxItems": "CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr darf höchstens 2 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-Id__maxLength": "CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-Id darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-Id__pattern": "CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-Id muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-SchmeNm-Cd__maxLength": "CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-SchmeNm-Prtry__maxLength": "CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-SchmeNm-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-SchmeNm-Prtry__pattern": "CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-SchmeNm-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-Issr__maxLength": "CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-Issr darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-Issr__pattern": "CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-Issr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt__pattern": "CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt muss dem Muster ^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)(?:Z|[+-][01]\\d:[0-5]\\d)?$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth__maxLength": "CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth__pattern": "CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth__maxLength": "CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth__pattern": "CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth__pattern": "CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth muss dem Muster ^[A-Z]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr__maxItems": "CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr darf höchstens 2 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-Id__maxLength": "CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-Id darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-Id__pattern": "CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-Id muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-SchmeNm-Cd__maxLength": "CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-SchmeNm-Prtry__maxLength": "CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-SchmeNm-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-SchmeNm-Prtry__pattern": "CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-SchmeNm-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-Issr__maxLength": "CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-Issr darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-Issr__pattern": "CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-Issr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-CtryOfRes__pattern": "CdtTrfTxInf-UltmtDbtr-CtryOfRes muss dem Muster ^[A-Z]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Nm__maxLength": "CdtTrfTxInf-Dbtr-Nm darf höchstens 140 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Nm__pattern": "CdtTrfTxInf-Dbtr-Nm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-Dept__maxLength": "CdtTrfTxInf-Dbtr-PstlAdr-Dept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-Dept__pattern": "CdtTrfTxInf-Dbtr-PstlAdr-Dept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-SubDept__maxLength": "CdtTrfTxInf-Dbtr-PstlAdr-SubDept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-SubDept__pattern": "CdtTrfTxInf-Dbtr-PstlAdr-SubDept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-StrtNm__maxLength": "CdtTrfTxInf-Dbtr-PstlAdr-StrtNm darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-StrtNm__pattern": "CdtTrfTxInf-Dbtr-PstlAdr-StrtNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-BldgNb__maxLength": "CdtTrfTxInf-Dbtr-PstlAdr-BldgNb darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-BldgNb__pattern": "CdtTrfTxInf-Dbtr-PstlAdr-BldgNb muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-BldgNm__maxLength": "CdtTrfTxInf-Dbtr-PstlAdr-BldgNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-BldgNm__pattern": "CdtTrfTxInf-Dbtr-PstlAdr-BldgNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-Flr__maxLength": "CdtTrfTxInf-Dbtr-PstlAdr-Flr darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-Flr__pattern": "CdtTrfTxInf-Dbtr-PstlAdr-Flr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-PstBx__maxLength": "CdtTrfTxInf-Dbtr-PstlAdr-PstBx darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-PstBx__pattern": "CdtTrfTxInf-Dbtr-PstlAdr-PstBx muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-Room__maxLength": "CdtTrfTxInf-Dbtr-PstlAdr-Room darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-Room__pattern": "CdtTrfTxInf-Dbtr-PstlAdr-Room muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-PstCd__maxLength": "CdtTrfTxInf-Dbtr-PstlAdr-PstCd darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-PstCd__pattern": "CdtTrfTxInf-Dbtr-PstlAdr-PstCd muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-TwnNm__maxLength": "CdtTrfTxInf-Dbtr-PstlAdr-TwnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-TwnNm__pattern": "CdtTrfTxInf-Dbtr-PstlAdr-TwnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-TwnLctnNm__maxLength": "CdtTrfTxInf-Dbtr-PstlAdr-TwnLctnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-TwnLctnNm__pattern": "CdtTrfTxInf-Dbtr-PstlAdr-TwnLctnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-DstrctNm__maxLength": "CdtTrfTxInf-Dbtr-PstlAdr-DstrctNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-DstrctNm__pattern": "CdtTrfTxInf-Dbtr-PstlAdr-DstrctNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-CtrySubDvsn__maxLength": "CdtTrfTxInf-Dbtr-PstlAdr-CtrySubDvsn darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-CtrySubDvsn__pattern": "CdtTrfTxInf-Dbtr-PstlAdr-CtrySubDvsn muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-Ctry__pattern": "CdtTrfTxInf-Dbtr-PstlAdr-Ctry muss dem Muster ^[A-Z]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-AdrLine__maxItems": "CdtTrfTxInf-Dbtr-PstlAdr-AdrLine darf höchstens 3 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-AdrLine__maxLength": "CdtTrfTxInf-Dbtr-PstlAdr-AdrLine darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-AdrLine__pattern": "CdtTrfTxInf-Dbtr-PstlAdr-AdrLine muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-AnyBIC__pattern": "CdtTrfTxInf-Dbtr-Id-OrgId-AnyBIC muss dem Muster ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-LEI__pattern": "CdtTrfTxInf-Dbtr-Id-OrgId-LEI muss dem Muster ^[A-Z0-9]{18,18}[0-9]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr__maxItems": "CdtTrfTxInf-Dbtr-Id-OrgId-Othr darf höchstens 2 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr-Id__maxLength": "CdtTrfTxInf-Dbtr-Id-OrgId-Othr-Id darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr-Id__pattern": "CdtTrfTxInf-Dbtr-Id-OrgId-Othr-Id muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr-SchmeNm-Cd__maxLength": "CdtTrfTxInf-Dbtr-Id-OrgId-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr-Issr__maxLength": "CdtTrfTxInf-Dbtr-Id-OrgId-Othr-Issr darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr-Issr__pattern": "CdtTrfTxInf-Dbtr-Id-OrgId-Othr-Issr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt__pattern": "CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt muss dem Muster ^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)(?:Z|[+-][01]\\d:[0-5]\\d)?$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth__maxLength": "CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth__pattern": "CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth__maxLength": "CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth__pattern": "CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth__pattern": "CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth muss dem Muster ^[A-Z]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr__maxItems": "CdtTrfTxInf-Dbtr-Id-PrvtId-Othr darf höchstens 2 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-Id__maxLength": "CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-Id darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-Id__pattern": "CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-Id muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-SchmeNm-Cd__maxLength": "CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-Issr__maxLength": "CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-Issr darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-Issr__pattern": "CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-Issr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-CtryOfRes__pattern": "CdtTrfTxInf-Dbtr-CtryOfRes muss dem Muster ^[A-Z]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-IBAN__pattern": "CdtTrfTxInf-DbtrAcct-Id-IBAN muss dem Muster ^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Id__maxLength": "CdtTrfTxInf-DbtrAcct-Id-Othr-Id darf höchstens 34 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Id__pattern": "CdtTrfTxInf-DbtrAcct-Id-Othr-Id muss dem Muster ^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Cd__maxLength": "CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Prtry__maxLength": "CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Prtry__pattern": "CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Issr__maxLength": "CdtTrfTxInf-DbtrAcct-Id-Othr-Issr darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Issr__pattern": "CdtTrfTxInf-DbtrAcct-Id-Othr-Issr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Tp-Cd__maxLength": "CdtTrfTxInf-DbtrAcct-Tp-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Tp-Prtry__maxLength": "CdtTrfTxInf-DbtrAcct-Tp-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Tp-Prtry__pattern": "CdtTrfTxInf-DbtrAcct-Tp-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Ccy__pattern": "CdtTrfTxInf-DbtrAcct-Ccy muss dem Muster ^[A-Z]{3,3}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Nm__maxLength": "CdtTrfTxInf-DbtrAcct-Nm darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Nm__pattern": "CdtTrfTxInf-DbtrAcct-Nm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp-Cd__maxLength": "CdtTrfTxInf-DbtrAcct-Prxy-Tp-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp-Prtry__maxLength": "CdtTrfTxInf-DbtrAcct-Prxy-Tp-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp-Prtry__pattern": "CdtTrfTxInf-DbtrAcct-Prxy-Tp-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Id__maxLength": "CdtTrfTxInf-DbtrAcct-Prxy-Id darf höchstens 320 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Id__pattern": "CdtTrfTxInf-DbtrAcct-Prxy-Id muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-TwnNm__conditional-required__3bd9e046+FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-TwnNm__required-conditional": "CdtTrfTxInf-UltmtDbtr-PstlAdr-TwnNm ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-UltmtDbtr-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnLctnNm oder DstrctNm oder CtrySubDvsn oder Ctry oder AdrLine) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Ctry__conditional-required__5c340cb4+FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Ctry__required-conditional": "CdtTrfTxInf-UltmtDbtr-PstlAdr-Ctry ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-UltmtDbtr-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnNm oder TwnLctnNm oder DstrctNm oder CtrySubDvsn oder AdrLine) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-Id__conditional-required__507f8566+FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-Id__required-conditional": "CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-Id ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-(SchmeNm oder Issr) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt__conditional-required__266056f4+FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt__required-conditional": "CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-(PrvcOfBirth oder CityOfBirth oder CtryOfBirth) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth__conditional-required__7e53a14a+FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth__required-conditional": "CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-(BirthDt oder PrvcOfBirth oder CtryOfBirth) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth__conditional-required__8b2842f1+FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth__required-conditional": "CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-(BirthDt oder PrvcOfBirth oder CityOfBirth) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-Id__conditional-required__46b7515c+FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-Id__required-conditional": "CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-Id ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-(SchmeNm oder Issr) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr-Id__conditional-required__b258445b+FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr-Id__required-conditional": "CdtTrfTxInf-Dbtr-Id-OrgId-Othr-Id ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-Dbtr-Id-OrgId-Othr-(SchmeNm oder Issr) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr-SchmeNm__conditional-required__fb0164af+FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr-SchmeNm-Cd__required-conditional": "CdtTrfTxInf-Dbtr-Id-OrgId-Othr-SchmeNm-Cd ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-Dbtr-Id-OrgId-Othr-(Id oder Issr) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt__conditional-required__c59412a0+FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt__required-conditional": "CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-(PrvcOfBirth oder CityOfBirth oder CtryOfBirth) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth__conditional-required__93b1225e+FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth__required-conditional": "CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-(BirthDt oder PrvcOfBirth oder CtryOfBirth) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth__conditional-required__4d0c9b05+FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth__required-conditional": "CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-(BirthDt oder PrvcOfBirth oder CityOfBirth) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-Id__conditional-required__bfcb487b+FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-Id__required-conditional": "CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-Id ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-(SchmeNm oder Issr) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-SchmeNm__conditional-required__28893ccf+FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-SchmeNm-Cd__required-conditional": "CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-SchmeNm-Cd ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-(Id oder Issr) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Id__conditional-required__1b578edd+FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Id__required-conditional": "CdtTrfTxInf-DbtrAcct-Id-Othr-Id ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-DbtrAcct-Id-Othr-(SchmeNm oder Issr) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Id__conditional-required__7d4fcc43+FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Id__required-conditional": "CdtTrfTxInf-DbtrAcct-Prxy-Id ist erforderlich, wenn CdtTrfTxInf-DbtrAcct-Prxy-Tp vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id__conditional-required__38a000a9+FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-IBAN__required-conditional": "CdtTrfTxInf-DbtrAcct-Id-IBAN ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-DbtrAcct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id__conditional-required__38a000a9+FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Id__required-conditional": "CdtTrfTxInf-DbtrAcct-Id-Othr-Id ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-DbtrAcct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Nm__maxLength": "CdtTrfTxInf-Cdtr-Nm darf höchstens 140 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Nm__pattern": "CdtTrfTxInf-Cdtr-Nm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-Dept__maxLength": "CdtTrfTxInf-Cdtr-PstlAdr-Dept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-Dept__pattern": "CdtTrfTxInf-Cdtr-PstlAdr-Dept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-SubDept__maxLength": "CdtTrfTxInf-Cdtr-PstlAdr-SubDept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-SubDept__pattern": "CdtTrfTxInf-Cdtr-PstlAdr-SubDept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-StrtNm__maxLength": "CdtTrfTxInf-Cdtr-PstlAdr-StrtNm darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-StrtNm__pattern": "CdtTrfTxInf-Cdtr-PstlAdr-StrtNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-BldgNb__maxLength": "CdtTrfTxInf-Cdtr-PstlAdr-BldgNb darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-BldgNb__pattern": "CdtTrfTxInf-Cdtr-PstlAdr-BldgNb muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-BldgNm__maxLength": "CdtTrfTxInf-Cdtr-PstlAdr-BldgNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-BldgNm__pattern": "CdtTrfTxInf-Cdtr-PstlAdr-BldgNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-Flr__maxLength": "CdtTrfTxInf-Cdtr-PstlAdr-Flr darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-Flr__pattern": "CdtTrfTxInf-Cdtr-PstlAdr-Flr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-PstBx__maxLength": "CdtTrfTxInf-Cdtr-PstlAdr-PstBx darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-PstBx__pattern": "CdtTrfTxInf-Cdtr-PstlAdr-PstBx muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-Room__maxLength": "CdtTrfTxInf-Cdtr-PstlAdr-Room darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-Room__pattern": "CdtTrfTxInf-Cdtr-PstlAdr-Room muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-PstCd__maxLength": "CdtTrfTxInf-Cdtr-PstlAdr-PstCd darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-PstCd__pattern": "CdtTrfTxInf-Cdtr-PstlAdr-PstCd muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-TwnNm__maxLength": "CdtTrfTxInf-Cdtr-PstlAdr-TwnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-TwnNm__pattern": "CdtTrfTxInf-Cdtr-PstlAdr-TwnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-TwnLctnNm__maxLength": "CdtTrfTxInf-Cdtr-PstlAdr-TwnLctnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-TwnLctnNm__pattern": "CdtTrfTxInf-Cdtr-PstlAdr-TwnLctnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-DstrctNm__maxLength": "CdtTrfTxInf-Cdtr-PstlAdr-DstrctNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-DstrctNm__pattern": "CdtTrfTxInf-Cdtr-PstlAdr-DstrctNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-CtrySubDvsn__maxLength": "CdtTrfTxInf-Cdtr-PstlAdr-CtrySubDvsn darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-CtrySubDvsn__pattern": "CdtTrfTxInf-Cdtr-PstlAdr-CtrySubDvsn muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-Ctry__pattern": "CdtTrfTxInf-Cdtr-PstlAdr-Ctry muss dem Muster ^[A-Z]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-AdrLine__maxItems": "CdtTrfTxInf-Cdtr-PstlAdr-AdrLine darf höchstens 3 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-AdrLine__maxLength": "CdtTrfTxInf-Cdtr-PstlAdr-AdrLine darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-AdrLine__pattern": "CdtTrfTxInf-Cdtr-PstlAdr-AdrLine muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-AnyBIC__pattern": "CdtTrfTxInf-Cdtr-Id-OrgId-AnyBIC muss dem Muster ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-LEI__pattern": "CdtTrfTxInf-Cdtr-Id-OrgId-LEI muss dem Muster ^[A-Z0-9]{18,18}[0-9]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr__maxItems": "CdtTrfTxInf-Cdtr-Id-OrgId-Othr darf höchstens 2 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-Id__maxLength": "CdtTrfTxInf-Cdtr-Id-OrgId-Othr-Id darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-Id__pattern": "CdtTrfTxInf-Cdtr-Id-OrgId-Othr-Id muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-SchmeNm-Cd__maxLength": "CdtTrfTxInf-Cdtr-Id-OrgId-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-SchmeNm-Prtry__maxLength": "CdtTrfTxInf-Cdtr-Id-OrgId-Othr-SchmeNm-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-SchmeNm-Prtry__pattern": "CdtTrfTxInf-Cdtr-Id-OrgId-Othr-SchmeNm-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-Issr__maxLength": "CdtTrfTxInf-Cdtr-Id-OrgId-Othr-Issr darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-Issr__pattern": "CdtTrfTxInf-Cdtr-Id-OrgId-Othr-Issr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt__pattern": "CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt muss dem Muster ^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)(?:Z|[+-][01]\\d:[0-5]\\d)?$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth__maxLength": "CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth__pattern": "CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth__maxLength": "CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth__pattern": "CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth__pattern": "CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth muss dem Muster ^[A-Z]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr__maxItems": "CdtTrfTxInf-Cdtr-Id-PrvtId-Othr darf höchstens 2 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-Id__maxLength": "CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-Id darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-Id__pattern": "CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-Id muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-SchmeNm-Cd__maxLength": "CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-SchmeNm-Prtry__maxLength": "CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-SchmeNm-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-SchmeNm-Prtry__pattern": "CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-SchmeNm-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-Issr__maxLength": "CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-Issr darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-Issr__pattern": "CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-Issr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-CtryOfRes__pattern": "CdtTrfTxInf-Cdtr-CtryOfRes muss dem Muster ^[A-Z]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-IBAN__pattern": "CdtTrfTxInf-CdtrAcct-Id-IBAN muss dem Muster ^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Id__maxLength": "CdtTrfTxInf-CdtrAcct-Id-Othr-Id darf höchstens 34 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Id__pattern": "CdtTrfTxInf-CdtrAcct-Id-Othr-Id muss dem Muster ^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Cd__maxLength": "CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Prtry__maxLength": "CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Prtry__pattern": "CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Issr__maxLength": "CdtTrfTxInf-CdtrAcct-Id-Othr-Issr darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Issr__pattern": "CdtTrfTxInf-CdtrAcct-Id-Othr-Issr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Tp-Cd__maxLength": "CdtTrfTxInf-CdtrAcct-Tp-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Tp-Prtry__maxLength": "CdtTrfTxInf-CdtrAcct-Tp-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Tp-Prtry__pattern": "CdtTrfTxInf-CdtrAcct-Tp-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Ccy__pattern": "CdtTrfTxInf-CdtrAcct-Ccy muss dem Muster ^[A-Z]{3,3}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Nm__maxLength": "CdtTrfTxInf-CdtrAcct-Nm darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Nm__pattern": "CdtTrfTxInf-CdtrAcct-Nm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp-Cd__maxLength": "CdtTrfTxInf-CdtrAcct-Prxy-Tp-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp-Prtry__maxLength": "CdtTrfTxInf-CdtrAcct-Prxy-Tp-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp-Prtry__pattern": "CdtTrfTxInf-CdtrAcct-Prxy-Tp-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Id__maxLength": "CdtTrfTxInf-CdtrAcct-Prxy-Id darf höchstens 320 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Id__pattern": "CdtTrfTxInf-CdtrAcct-Prxy-Id muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Nm__maxLength": "CdtTrfTxInf-UltmtCdtr-Nm darf höchstens 140 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Nm__pattern": "CdtTrfTxInf-UltmtCdtr-Nm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Dept__maxLength": "CdtTrfTxInf-UltmtCdtr-PstlAdr-Dept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Dept__pattern": "CdtTrfTxInf-UltmtCdtr-PstlAdr-Dept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-SubDept__maxLength": "CdtTrfTxInf-UltmtCdtr-PstlAdr-SubDept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-SubDept__pattern": "CdtTrfTxInf-UltmtCdtr-PstlAdr-SubDept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-StrtNm__maxLength": "CdtTrfTxInf-UltmtCdtr-PstlAdr-StrtNm darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-StrtNm__pattern": "CdtTrfTxInf-UltmtCdtr-PstlAdr-StrtNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-BldgNb__maxLength": "CdtTrfTxInf-UltmtCdtr-PstlAdr-BldgNb darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-BldgNb__pattern": "CdtTrfTxInf-UltmtCdtr-PstlAdr-BldgNb muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-BldgNm__maxLength": "CdtTrfTxInf-UltmtCdtr-PstlAdr-BldgNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-BldgNm__pattern": "CdtTrfTxInf-UltmtCdtr-PstlAdr-BldgNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Flr__maxLength": "CdtTrfTxInf-UltmtCdtr-PstlAdr-Flr darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Flr__pattern": "CdtTrfTxInf-UltmtCdtr-PstlAdr-Flr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-PstBx__maxLength": "CdtTrfTxInf-UltmtCdtr-PstlAdr-PstBx darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-PstBx__pattern": "CdtTrfTxInf-UltmtCdtr-PstlAdr-PstBx muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Room__maxLength": "CdtTrfTxInf-UltmtCdtr-PstlAdr-Room darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Room__pattern": "CdtTrfTxInf-UltmtCdtr-PstlAdr-Room muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-PstCd__maxLength": "CdtTrfTxInf-UltmtCdtr-PstlAdr-PstCd darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-PstCd__pattern": "CdtTrfTxInf-UltmtCdtr-PstlAdr-PstCd muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-TwnNm__maxLength": "CdtTrfTxInf-UltmtCdtr-PstlAdr-TwnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-TwnNm__pattern": "CdtTrfTxInf-UltmtCdtr-PstlAdr-TwnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-TwnLctnNm__maxLength": "CdtTrfTxInf-UltmtCdtr-PstlAdr-TwnLctnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-TwnLctnNm__pattern": "CdtTrfTxInf-UltmtCdtr-PstlAdr-TwnLctnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-DstrctNm__maxLength": "CdtTrfTxInf-UltmtCdtr-PstlAdr-DstrctNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-DstrctNm__pattern": "CdtTrfTxInf-UltmtCdtr-PstlAdr-DstrctNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-CtrySubDvsn__maxLength": "CdtTrfTxInf-UltmtCdtr-PstlAdr-CtrySubDvsn darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-CtrySubDvsn__pattern": "CdtTrfTxInf-UltmtCdtr-PstlAdr-CtrySubDvsn muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Ctry__pattern": "CdtTrfTxInf-UltmtCdtr-PstlAdr-Ctry muss dem Muster ^[A-Z]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-AdrLine__maxItems": "CdtTrfTxInf-UltmtCdtr-PstlAdr-AdrLine darf höchstens 2 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-AdrLine__maxLength": "CdtTrfTxInf-UltmtCdtr-PstlAdr-AdrLine darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-AdrLine__pattern": "CdtTrfTxInf-UltmtCdtr-PstlAdr-AdrLine muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-AnyBIC__pattern": "CdtTrfTxInf-UltmtCdtr-Id-OrgId-AnyBIC muss dem Muster ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-LEI__pattern": "CdtTrfTxInf-UltmtCdtr-Id-OrgId-LEI muss dem Muster ^[A-Z0-9]{18,18}[0-9]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr__maxItems": "CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr darf höchstens 2 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-Id__maxLength": "CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-Id darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-Id__pattern": "CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-Id muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-SchmeNm-Cd__maxLength": "CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-SchmeNm-Prtry__maxLength": "CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-SchmeNm-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-SchmeNm-Prtry__pattern": "CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-SchmeNm-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-Issr__maxLength": "CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-Issr darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-Issr__pattern": "CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-Issr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt__pattern": "CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt muss dem Muster ^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)(?:Z|[+-][01]\\d:[0-5]\\d)?$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth__maxLength": "CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth__pattern": "CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth__maxLength": "CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth__pattern": "CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth__pattern": "CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth muss dem Muster ^[A-Z]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr__maxItems": "CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr darf höchstens 2 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-Id__maxLength": "CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-Id darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-Id__pattern": "CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-Id muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-SchmeNm-Cd__maxLength": "CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-SchmeNm-Prtry__maxLength": "CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-SchmeNm-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-SchmeNm-Prtry__pattern": "CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-SchmeNm-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-Issr__maxLength": "CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-Issr darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-Issr__pattern": "CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-Issr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-CtryOfRes__pattern": "CdtTrfTxInf-UltmtCdtr-CtryOfRes muss dem Muster ^[A-Z]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-Id__conditional-required__9c338193+FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-Id__required-conditional": "CdtTrfTxInf-Cdtr-Id-OrgId-Othr-Id ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-Cdtr-Id-OrgId-Othr-(SchmeNm oder Issr) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt__conditional-required__8b1ad4a1+FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt__required-conditional": "CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-(PrvcOfBirth oder CityOfBirth oder CtryOfBirth) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth__conditional-required__a86ee7df+FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth__required-conditional": "CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-(BirthDt oder PrvcOfBirth oder CtryOfBirth) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth__conditional-required__dcb21fc4+FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth__required-conditional": "CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-(BirthDt oder PrvcOfBirth oder CityOfBirth) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-Id__conditional-required__2744b7e9+FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-Id__required-conditional": "CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-Id ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-(SchmeNm oder Issr) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Id__conditional-required__88dbc37c+FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Id__required-conditional": "CdtTrfTxInf-CdtrAcct-Id-Othr-Id ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-CdtrAcct-Id-Othr-(SchmeNm oder Issr) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Id__conditional-required__5fc34983+FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Id__required-conditional": "CdtTrfTxInf-CdtrAcct-Prxy-Id ist erforderlich, wenn CdtTrfTxInf-CdtrAcct-Prxy-Tp vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id__conditional-required__c30cda48+FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-IBAN__required-conditional": "CdtTrfTxInf-CdtrAcct-Id-IBAN ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-CdtrAcct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id__conditional-required__c30cda48+FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Id__required-conditional": "CdtTrfTxInf-CdtrAcct-Id-Othr-Id ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-CdtrAcct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-TwnNm__conditional-required__c673d866+FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-TwnNm__required-conditional": "CdtTrfTxInf-UltmtCdtr-PstlAdr-TwnNm ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-UltmtCdtr-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnLctnNm oder DstrctNm oder CtrySubDvsn oder Ctry oder AdrLine) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Ctry__conditional-required__e4ec6494+FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Ctry__required-conditional": "CdtTrfTxInf-UltmtCdtr-PstlAdr-Ctry ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-UltmtCdtr-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnNm oder TwnLctnNm oder DstrctNm oder CtrySubDvsn oder AdrLine) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-Id__conditional-required__22edda67+FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-Id__required-conditional": "CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-Id ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-(SchmeNm oder Issr) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt__conditional-required__783eca15+FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt__required-conditional": "CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-(PrvcOfBirth oder CityOfBirth oder CtryOfBirth) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth__conditional-required__9236dc6b+FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth__required-conditional": "CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-(BirthDt oder PrvcOfBirth oder CtryOfBirth) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth__conditional-required__c8859350+FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth__required-conditional": "CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-(BirthDt oder PrvcOfBirth oder CityOfBirth) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-Id__conditional-required__cc6cd11d+FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-Id__required-conditional": "CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-Id ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-(SchmeNm oder Issr) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-BICFI__pattern": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-BICFI muss dem Muster ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd__maxLength": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd darf höchstens 5 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId__maxLength": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId darf höchstens 28 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId__pattern": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-LEI__pattern": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-LEI muss dem Muster ^[A-Z0-9]{18,18}[0-9]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-Nm__maxLength": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-Nm darf höchstens 140 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-Nm__pattern": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-Nm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Dept__maxLength": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Dept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Dept__pattern": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Dept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-SubDept__maxLength": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-SubDept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-SubDept__pattern": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-SubDept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-StrtNm__maxLength": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-StrtNm darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-StrtNm__pattern": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-StrtNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNb__maxLength": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNb darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNb__pattern": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNb muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNm__maxLength": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNm__pattern": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Flr__maxLength": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Flr darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Flr__pattern": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Flr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstBx__maxLength": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstBx darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstBx__pattern": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstBx muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Room__maxLength": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Room darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Room__pattern": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Room muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstCd__maxLength": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstCd darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstCd__pattern": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstCd muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnNm__maxLength": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnNm__pattern": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnLctnNm__maxLength": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnLctnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnLctnNm__pattern": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnLctnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-DstrctNm__maxLength": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-DstrctNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-DstrctNm__pattern": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-DstrctNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-CtrySubDvsn__maxLength": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-CtrySubDvsn darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-CtrySubDvsn__pattern": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-CtrySubDvsn muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Ctry__pattern": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Ctry muss dem Muster ^[A-Z]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-AdrLine__maxItems": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-AdrLine darf höchstens 3 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-AdrLine__maxLength": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-AdrLine darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-AdrLine__pattern": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-AdrLine muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-IBAN__pattern": "CdtTrfTxInf-PrvsInstgAgt1Acct-Id-IBAN muss dem Muster ^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Id__maxLength": "CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Id darf höchstens 34 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Id__pattern": "CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Id muss dem Muster ^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-Cd__maxLength": "CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-Prtry__maxLength": "CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-Prtry__pattern": "CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Issr__maxLength": "CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Issr darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Issr__pattern": "CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Issr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-Cd__maxLength": "CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-Prtry__maxLength": "CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-Prtry__pattern": "CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Ccy__pattern": "CdtTrfTxInf-PrvsInstgAgt1Acct-Ccy muss dem Muster ^[A-Z]{3,3}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Nm__maxLength": "CdtTrfTxInf-PrvsInstgAgt1Acct-Nm darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Nm__pattern": "CdtTrfTxInf-PrvsInstgAgt1Acct-Nm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Cd__maxLength": "CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Prtry__maxLength": "CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Prtry__pattern": "CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Id__maxLength": "CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Id darf höchstens 320 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Id__pattern": "CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Id muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-BICFI__pattern": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-BICFI muss dem Muster ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd__maxLength": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd darf höchstens 5 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId__maxLength": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId darf höchstens 28 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId__pattern": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-LEI__pattern": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-LEI muss dem Muster ^[A-Z0-9]{18,18}[0-9]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-Nm__maxLength": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-Nm darf höchstens 140 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-Nm__pattern": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-Nm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Dept__maxLength": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Dept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Dept__pattern": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Dept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-SubDept__maxLength": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-SubDept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-SubDept__pattern": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-SubDept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-StrtNm__maxLength": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-StrtNm darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-StrtNm__pattern": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-StrtNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNb__maxLength": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNb darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNb__pattern": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNb muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNm__maxLength": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNm__pattern": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Flr__maxLength": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Flr darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Flr__pattern": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Flr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstBx__maxLength": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstBx darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstBx__pattern": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstBx muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Room__maxLength": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Room darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Room__pattern": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Room muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstCd__maxLength": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstCd darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstCd__pattern": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstCd muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnNm__maxLength": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnNm__pattern": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnLctnNm__maxLength": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnLctnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnLctnNm__pattern": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnLctnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-DstrctNm__maxLength": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-DstrctNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-DstrctNm__pattern": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-DstrctNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-CtrySubDvsn__maxLength": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-CtrySubDvsn darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-CtrySubDvsn__pattern": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-CtrySubDvsn muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Ctry__pattern": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Ctry muss dem Muster ^[A-Z]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-AdrLine__maxItems": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-AdrLine darf höchstens 3 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-AdrLine__maxLength": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-AdrLine darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-AdrLine__pattern": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-AdrLine muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-IBAN__pattern": "CdtTrfTxInf-PrvsInstgAgt2Acct-Id-IBAN muss dem Muster ^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Id__maxLength": "CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Id darf höchstens 34 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Id__pattern": "CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Id muss dem Muster ^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-Cd__maxLength": "CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-Prtry__maxLength": "CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-Prtry__pattern": "CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Issr__maxLength": "CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Issr darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Issr__pattern": "CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Issr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-Cd__maxLength": "CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-Prtry__maxLength": "CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-Prtry__pattern": "CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Ccy__pattern": "CdtTrfTxInf-PrvsInstgAgt2Acct-Ccy muss dem Muster ^[A-Z]{3,3}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Nm__maxLength": "CdtTrfTxInf-PrvsInstgAgt2Acct-Nm darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Nm__pattern": "CdtTrfTxInf-PrvsInstgAgt2Acct-Nm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Cd__maxLength": "CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Prtry__maxLength": "CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Prtry__pattern": "CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Id__maxLength": "CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Id darf höchstens 320 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Id__pattern": "CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Id muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-BICFI__pattern": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-BICFI muss dem Muster ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd__maxLength": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd darf höchstens 5 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId__maxLength": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId darf höchstens 28 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId__pattern": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-LEI__pattern": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-LEI muss dem Muster ^[A-Z0-9]{18,18}[0-9]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-Nm__maxLength": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-Nm darf höchstens 140 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-Nm__pattern": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-Nm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Dept__maxLength": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Dept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Dept__pattern": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Dept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-SubDept__maxLength": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-SubDept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-SubDept__pattern": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-SubDept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-StrtNm__maxLength": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-StrtNm darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-StrtNm__pattern": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-StrtNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNb__maxLength": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNb darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNb__pattern": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNb muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNm__maxLength": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNm__pattern": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Flr__maxLength": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Flr darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Flr__pattern": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Flr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstBx__maxLength": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstBx darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstBx__pattern": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstBx muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Room__maxLength": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Room darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Room__pattern": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Room muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstCd__maxLength": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstCd darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstCd__pattern": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstCd muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnNm__maxLength": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnNm__pattern": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnLctnNm__maxLength": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnLctnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnLctnNm__pattern": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnLctnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-DstrctNm__maxLength": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-DstrctNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-DstrctNm__pattern": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-DstrctNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-CtrySubDvsn__maxLength": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-CtrySubDvsn darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-CtrySubDvsn__pattern": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-CtrySubDvsn muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Ctry__pattern": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Ctry muss dem Muster ^[A-Z]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-AdrLine__maxItems": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-AdrLine darf höchstens 3 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-AdrLine__maxLength": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-AdrLine darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-AdrLine__pattern": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-AdrLine muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-IBAN__pattern": "CdtTrfTxInf-PrvsInstgAgt3Acct-Id-IBAN muss dem Muster ^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Id__maxLength": "CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Id darf höchstens 34 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Id__pattern": "CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Id muss dem Muster ^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-Cd__maxLength": "CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-Prtry__maxLength": "CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-Prtry__pattern": "CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Issr__maxLength": "CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Issr darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Issr__pattern": "CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Issr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-Cd__maxLength": "CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-Prtry__maxLength": "CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-Prtry__pattern": "CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Ccy__pattern": "CdtTrfTxInf-PrvsInstgAgt3Acct-Ccy muss dem Muster ^[A-Z]{3,3}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Nm__maxLength": "CdtTrfTxInf-PrvsInstgAgt3Acct-Nm darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Nm__pattern": "CdtTrfTxInf-PrvsInstgAgt3Acct-Nm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Cd__maxLength": "CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Prtry__maxLength": "CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Prtry__pattern": "CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Id__maxLength": "CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Id darf höchstens 320 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Id__pattern": "CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Id muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-BICFI__pattern": "CdtTrfTxInf-InstgAgt-FinInstnId-BICFI muss dem Muster ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-BICFI__required": "CdtTrfTxInf-InstgAgt-FinInstnId-BICFI ist erforderlich.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd__maxLength": "CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd darf höchstens 5 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId__maxLength": "CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId darf höchstens 28 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId__pattern": "CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-LEI__pattern": "CdtTrfTxInf-InstgAgt-FinInstnId-LEI muss dem Muster ^[A-Z0-9]{18,18}[0-9]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-BICFI__pattern": "CdtTrfTxInf-InstdAgt-FinInstnId-BICFI muss dem Muster ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-BICFI__required": "CdtTrfTxInf-InstdAgt-FinInstnId-BICFI ist erforderlich.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd__maxLength": "CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd darf höchstens 5 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId__maxLength": "CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId darf höchstens 28 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId__pattern": "CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-LEI__pattern": "CdtTrfTxInf-InstdAgt-FinInstnId-LEI muss dem Muster ^[A-Z0-9]{18,18}[0-9]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-BICFI__pattern": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-BICFI muss dem Muster ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd__maxLength": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd darf höchstens 5 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId__maxLength": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId darf höchstens 28 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId__pattern": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-LEI__pattern": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-LEI muss dem Muster ^[A-Z0-9]{18,18}[0-9]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-Nm__maxLength": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-Nm darf höchstens 140 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-Nm__pattern": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-Nm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Dept__maxLength": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Dept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Dept__pattern": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Dept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-SubDept__maxLength": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-SubDept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-SubDept__pattern": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-SubDept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-StrtNm__maxLength": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-StrtNm darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-StrtNm__pattern": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-StrtNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNb__maxLength": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNb darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNb__pattern": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNb muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNm__maxLength": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNm__pattern": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Flr__maxLength": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Flr darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Flr__pattern": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Flr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstBx__maxLength": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstBx darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstBx__pattern": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstBx muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Room__maxLength": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Room darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Room__pattern": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Room muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstCd__maxLength": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstCd darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstCd__pattern": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstCd muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnNm__maxLength": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnNm__pattern": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnLctnNm__maxLength": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnLctnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnLctnNm__pattern": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnLctnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-DstrctNm__maxLength": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-DstrctNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-DstrctNm__pattern": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-DstrctNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-CtrySubDvsn__maxLength": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-CtrySubDvsn darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-CtrySubDvsn__pattern": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-CtrySubDvsn muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Ctry__pattern": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Ctry muss dem Muster ^[A-Z]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-AdrLine__maxItems": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-AdrLine darf höchstens 3 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-AdrLine__maxLength": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-AdrLine darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-AdrLine__pattern": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-AdrLine muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-IBAN__pattern": "CdtTrfTxInf-IntrmyAgt1Acct-Id-IBAN muss dem Muster ^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Id__maxLength": "CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Id darf höchstens 34 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Id__pattern": "CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Id muss dem Muster ^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-Cd__maxLength": "CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-Prtry__maxLength": "CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-Prtry__pattern": "CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Issr__maxLength": "CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Issr darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Issr__pattern": "CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Issr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Tp-Cd__maxLength": "CdtTrfTxInf-IntrmyAgt1Acct-Tp-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Tp-Prtry__maxLength": "CdtTrfTxInf-IntrmyAgt1Acct-Tp-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Tp-Prtry__pattern": "CdtTrfTxInf-IntrmyAgt1Acct-Tp-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Ccy__pattern": "CdtTrfTxInf-IntrmyAgt1Acct-Ccy muss dem Muster ^[A-Z]{3,3}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Nm__maxLength": "CdtTrfTxInf-IntrmyAgt1Acct-Nm darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Nm__pattern": "CdtTrfTxInf-IntrmyAgt1Acct-Nm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Cd__maxLength": "CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Prtry__maxLength": "CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Prtry__pattern": "CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Id__maxLength": "CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Id darf höchstens 320 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Id__pattern": "CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Id muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-BICFI__pattern": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-BICFI muss dem Muster ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd__maxLength": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd darf höchstens 5 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId__maxLength": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId darf höchstens 28 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId__pattern": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-LEI__pattern": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-LEI muss dem Muster ^[A-Z0-9]{18,18}[0-9]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-Nm__maxLength": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-Nm darf höchstens 140 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-Nm__pattern": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-Nm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Dept__maxLength": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Dept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Dept__pattern": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Dept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-SubDept__maxLength": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-SubDept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-SubDept__pattern": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-SubDept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-StrtNm__maxLength": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-StrtNm darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-StrtNm__pattern": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-StrtNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNb__maxLength": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNb darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNb__pattern": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNb muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNm__maxLength": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNm__pattern": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Flr__maxLength": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Flr darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Flr__pattern": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Flr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstBx__maxLength": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstBx darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstBx__pattern": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstBx muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Room__maxLength": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Room darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Room__pattern": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Room muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstCd__maxLength": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstCd darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstCd__pattern": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstCd muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnNm__maxLength": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnNm__pattern": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnLctnNm__maxLength": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnLctnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnLctnNm__pattern": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnLctnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-DstrctNm__maxLength": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-DstrctNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-DstrctNm__pattern": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-DstrctNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-CtrySubDvsn__maxLength": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-CtrySubDvsn darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-CtrySubDvsn__pattern": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-CtrySubDvsn muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Ctry__pattern": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Ctry muss dem Muster ^[A-Z]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-AdrLine__maxItems": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-AdrLine darf höchstens 3 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-AdrLine__maxLength": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-AdrLine darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-AdrLine__pattern": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-AdrLine muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-IBAN__pattern": "CdtTrfTxInf-IntrmyAgt2Acct-Id-IBAN muss dem Muster ^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Id__maxLength": "CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Id darf höchstens 34 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Id__pattern": "CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Id muss dem Muster ^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-Cd__maxLength": "CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-Prtry__maxLength": "CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-Prtry__pattern": "CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Issr__maxLength": "CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Issr darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Issr__pattern": "CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Issr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Tp-Cd__maxLength": "CdtTrfTxInf-IntrmyAgt2Acct-Tp-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Tp-Prtry__maxLength": "CdtTrfTxInf-IntrmyAgt2Acct-Tp-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Tp-Prtry__pattern": "CdtTrfTxInf-IntrmyAgt2Acct-Tp-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Ccy__pattern": "CdtTrfTxInf-IntrmyAgt2Acct-Ccy muss dem Muster ^[A-Z]{3,3}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Nm__maxLength": "CdtTrfTxInf-IntrmyAgt2Acct-Nm darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Nm__pattern": "CdtTrfTxInf-IntrmyAgt2Acct-Nm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Cd__maxLength": "CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Prtry__maxLength": "CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Prtry__pattern": "CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Id__maxLength": "CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Id darf höchstens 320 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Id__pattern": "CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Id muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-BICFI__pattern": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-BICFI muss dem Muster ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd__maxLength": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd darf höchstens 5 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId__maxLength": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId darf höchstens 28 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId__pattern": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-LEI__pattern": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-LEI muss dem Muster ^[A-Z0-9]{18,18}[0-9]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-Nm__maxLength": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-Nm darf höchstens 140 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-Nm__pattern": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-Nm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Dept__maxLength": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Dept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Dept__pattern": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Dept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-SubDept__maxLength": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-SubDept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-SubDept__pattern": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-SubDept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-StrtNm__maxLength": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-StrtNm darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-StrtNm__pattern": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-StrtNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNb__maxLength": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNb darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNb__pattern": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNb muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNm__maxLength": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNm__pattern": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Flr__maxLength": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Flr darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Flr__pattern": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Flr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstBx__maxLength": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstBx darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstBx__pattern": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstBx muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Room__maxLength": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Room darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Room__pattern": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Room muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstCd__maxLength": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstCd darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstCd__pattern": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstCd muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnNm__maxLength": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnNm__pattern": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnLctnNm__maxLength": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnLctnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnLctnNm__pattern": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnLctnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-DstrctNm__maxLength": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-DstrctNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-DstrctNm__pattern": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-DstrctNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-CtrySubDvsn__maxLength": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-CtrySubDvsn darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-CtrySubDvsn__pattern": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-CtrySubDvsn muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Ctry__pattern": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Ctry muss dem Muster ^[A-Z]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-AdrLine__maxItems": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-AdrLine darf höchstens 3 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-AdrLine__maxLength": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-AdrLine darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-AdrLine__pattern": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-AdrLine muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-IBAN__pattern": "CdtTrfTxInf-IntrmyAgt3Acct-Id-IBAN muss dem Muster ^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Id__maxLength": "CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Id darf höchstens 34 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Id__pattern": "CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Id muss dem Muster ^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-Cd__maxLength": "CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-Prtry__maxLength": "CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-Prtry__pattern": "CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Issr__maxLength": "CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Issr darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Issr__pattern": "CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Issr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Tp-Cd__maxLength": "CdtTrfTxInf-IntrmyAgt3Acct-Tp-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Tp-Prtry__maxLength": "CdtTrfTxInf-IntrmyAgt3Acct-Tp-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Tp-Prtry__pattern": "CdtTrfTxInf-IntrmyAgt3Acct-Tp-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Ccy__pattern": "CdtTrfTxInf-IntrmyAgt3Acct-Ccy muss dem Muster ^[A-Z]{3,3}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Nm__maxLength": "CdtTrfTxInf-IntrmyAgt3Acct-Nm darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Nm__pattern": "CdtTrfTxInf-IntrmyAgt3Acct-Nm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Cd__maxLength": "CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Prtry__maxLength": "CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Prtry__pattern": "CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Id__maxLength": "CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Id darf höchstens 320 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Id__pattern": "CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Id muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Nm__maxLength": "CdtTrfTxInf-InitgPty-Nm darf höchstens 140 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Nm__pattern": "CdtTrfTxInf-InitgPty-Nm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-Dept__maxLength": "CdtTrfTxInf-InitgPty-PstlAdr-Dept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-Dept__pattern": "CdtTrfTxInf-InitgPty-PstlAdr-Dept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-SubDept__maxLength": "CdtTrfTxInf-InitgPty-PstlAdr-SubDept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-SubDept__pattern": "CdtTrfTxInf-InitgPty-PstlAdr-SubDept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-StrtNm__maxLength": "CdtTrfTxInf-InitgPty-PstlAdr-StrtNm darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-StrtNm__pattern": "CdtTrfTxInf-InitgPty-PstlAdr-StrtNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-BldgNb__maxLength": "CdtTrfTxInf-InitgPty-PstlAdr-BldgNb darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-BldgNb__pattern": "CdtTrfTxInf-InitgPty-PstlAdr-BldgNb muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-BldgNm__maxLength": "CdtTrfTxInf-InitgPty-PstlAdr-BldgNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-BldgNm__pattern": "CdtTrfTxInf-InitgPty-PstlAdr-BldgNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-Flr__maxLength": "CdtTrfTxInf-InitgPty-PstlAdr-Flr darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-Flr__pattern": "CdtTrfTxInf-InitgPty-PstlAdr-Flr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-PstBx__maxLength": "CdtTrfTxInf-InitgPty-PstlAdr-PstBx darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-PstBx__pattern": "CdtTrfTxInf-InitgPty-PstlAdr-PstBx muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-Room__maxLength": "CdtTrfTxInf-InitgPty-PstlAdr-Room darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-Room__pattern": "CdtTrfTxInf-InitgPty-PstlAdr-Room muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-PstCd__maxLength": "CdtTrfTxInf-InitgPty-PstlAdr-PstCd darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-PstCd__pattern": "CdtTrfTxInf-InitgPty-PstlAdr-PstCd muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-TwnNm__maxLength": "CdtTrfTxInf-InitgPty-PstlAdr-TwnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-TwnNm__pattern": "CdtTrfTxInf-InitgPty-PstlAdr-TwnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-TwnLctnNm__maxLength": "CdtTrfTxInf-InitgPty-PstlAdr-TwnLctnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-TwnLctnNm__pattern": "CdtTrfTxInf-InitgPty-PstlAdr-TwnLctnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-DstrctNm__maxLength": "CdtTrfTxInf-InitgPty-PstlAdr-DstrctNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-DstrctNm__pattern": "CdtTrfTxInf-InitgPty-PstlAdr-DstrctNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-CtrySubDvsn__maxLength": "CdtTrfTxInf-InitgPty-PstlAdr-CtrySubDvsn darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-CtrySubDvsn__pattern": "CdtTrfTxInf-InitgPty-PstlAdr-CtrySubDvsn muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-Ctry__pattern": "CdtTrfTxInf-InitgPty-PstlAdr-Ctry muss dem Muster ^[A-Z]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-AdrLine__maxItems": "CdtTrfTxInf-InitgPty-PstlAdr-AdrLine darf höchstens 2 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-AdrLine__maxLength": "CdtTrfTxInf-InitgPty-PstlAdr-AdrLine darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-AdrLine__pattern": "CdtTrfTxInf-InitgPty-PstlAdr-AdrLine muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-AnyBIC__pattern": "CdtTrfTxInf-InitgPty-Id-OrgId-AnyBIC muss dem Muster ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-LEI__pattern": "CdtTrfTxInf-InitgPty-Id-OrgId-LEI muss dem Muster ^[A-Z0-9]{18,18}[0-9]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr__maxItems": "CdtTrfTxInf-InitgPty-Id-OrgId-Othr darf höchstens 2 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-Id__maxLength": "CdtTrfTxInf-InitgPty-Id-OrgId-Othr-Id darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-Id__pattern": "CdtTrfTxInf-InitgPty-Id-OrgId-Othr-Id muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-SchmeNm-Cd__maxLength": "CdtTrfTxInf-InitgPty-Id-OrgId-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-SchmeNm-Prtry__maxLength": "CdtTrfTxInf-InitgPty-Id-OrgId-Othr-SchmeNm-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-SchmeNm-Prtry__pattern": "CdtTrfTxInf-InitgPty-Id-OrgId-Othr-SchmeNm-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-Issr__maxLength": "CdtTrfTxInf-InitgPty-Id-OrgId-Othr-Issr darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-Issr__pattern": "CdtTrfTxInf-InitgPty-Id-OrgId-Othr-Issr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-BirthDt__pattern": "CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-BirthDt muss dem Muster ^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)(?:Z|[+-][01]\\d:[0-5]\\d)?$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth__maxLength": "CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth__pattern": "CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth__maxLength": "CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth__pattern": "CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth__pattern": "CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth muss dem Muster ^[A-Z]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr__maxItems": "CdtTrfTxInf-InitgPty-Id-PrvtId-Othr darf höchstens 2 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-Id__maxLength": "CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-Id darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-Id__pattern": "CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-Id muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-SchmeNm-Cd__maxLength": "CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-SchmeNm-Prtry__maxLength": "CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-SchmeNm-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-SchmeNm-Prtry__pattern": "CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-SchmeNm-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-Issr__maxLength": "CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-Issr darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-Issr__pattern": "CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-Issr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-CtryOfRes__pattern": "CdtTrfTxInf-InitgPty-CtryOfRes muss dem Muster ^[A-Z]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-BICFI__pattern": "CdtTrfTxInf-DbtrAgt-FinInstnId-BICFI muss dem Muster ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd__maxLength": "CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd darf höchstens 5 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId__maxLength": "CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId darf höchstens 28 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId__pattern": "CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-LEI__pattern": "CdtTrfTxInf-DbtrAgt-FinInstnId-LEI muss dem Muster ^[A-Z0-9]{18,18}[0-9]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-Nm__maxLength": "CdtTrfTxInf-DbtrAgt-FinInstnId-Nm darf höchstens 140 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-Nm__pattern": "CdtTrfTxInf-DbtrAgt-FinInstnId-Nm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Dept__maxLength": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Dept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Dept__pattern": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Dept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-SubDept__maxLength": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-SubDept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-SubDept__pattern": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-SubDept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-StrtNm__maxLength": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-StrtNm darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-StrtNm__pattern": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-StrtNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNb__maxLength": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNb darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNb__pattern": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNb muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNm__maxLength": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNm__pattern": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Flr__maxLength": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Flr darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Flr__pattern": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Flr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstBx__maxLength": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstBx darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstBx__pattern": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstBx muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Room__maxLength": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Room darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Room__pattern": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Room muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstCd__maxLength": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstCd darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstCd__pattern": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstCd muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnNm__maxLength": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnNm__pattern": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnLctnNm__maxLength": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnLctnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnLctnNm__pattern": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnLctnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-DstrctNm__maxLength": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-DstrctNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-DstrctNm__pattern": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-DstrctNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-CtrySubDvsn__maxLength": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-CtrySubDvsn darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-CtrySubDvsn__pattern": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-CtrySubDvsn muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Ctry__pattern": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Ctry muss dem Muster ^[A-Z]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-AdrLine__maxItems": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-AdrLine darf höchstens 3 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-AdrLine__maxLength": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-AdrLine darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-AdrLine__pattern": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-AdrLine muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-IBAN__pattern": "CdtTrfTxInf-DbtrAgtAcct-Id-IBAN muss dem Muster ^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Id__maxLength": "CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Id darf höchstens 34 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Id__pattern": "CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Id muss dem Muster ^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-Cd__maxLength": "CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-Prtry__maxLength": "CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-Prtry__pattern": "CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Issr__maxLength": "CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Issr darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Issr__pattern": "CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Issr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Tp-Cd__maxLength": "CdtTrfTxInf-DbtrAgtAcct-Tp-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Tp-Prtry__maxLength": "CdtTrfTxInf-DbtrAgtAcct-Tp-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Tp-Prtry__pattern": "CdtTrfTxInf-DbtrAgtAcct-Tp-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Ccy__pattern": "CdtTrfTxInf-DbtrAgtAcct-Ccy muss dem Muster ^[A-Z]{3,3}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Nm__maxLength": "CdtTrfTxInf-DbtrAgtAcct-Nm darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Nm__pattern": "CdtTrfTxInf-DbtrAgtAcct-Nm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Cd__maxLength": "CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Prtry__maxLength": "CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Prtry__pattern": "CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Id__maxLength": "CdtTrfTxInf-DbtrAgtAcct-Prxy-Id darf höchstens 320 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Id__pattern": "CdtTrfTxInf-DbtrAgtAcct-Prxy-Id muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-BICFI__pattern": "CdtTrfTxInf-CdtrAgt-FinInstnId-BICFI muss dem Muster ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd__maxLength": "CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd darf höchstens 5 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId__maxLength": "CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId darf höchstens 28 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId__pattern": "CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-LEI__pattern": "CdtTrfTxInf-CdtrAgt-FinInstnId-LEI muss dem Muster ^[A-Z0-9]{18,18}[0-9]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-Nm__maxLength": "CdtTrfTxInf-CdtrAgt-FinInstnId-Nm darf höchstens 140 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-Nm__pattern": "CdtTrfTxInf-CdtrAgt-FinInstnId-Nm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Dept__maxLength": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Dept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Dept__pattern": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Dept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-SubDept__maxLength": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-SubDept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-SubDept__pattern": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-SubDept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-StrtNm__maxLength": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-StrtNm darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-StrtNm__pattern": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-StrtNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNb__maxLength": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNb darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNb__pattern": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNb muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNm__maxLength": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNm__pattern": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Flr__maxLength": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Flr darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Flr__pattern": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Flr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstBx__maxLength": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstBx darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstBx__pattern": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstBx muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Room__maxLength": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Room darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Room__pattern": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Room muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstCd__maxLength": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstCd darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstCd__pattern": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstCd muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnNm__maxLength": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnNm__pattern": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnLctnNm__maxLength": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnLctnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnLctnNm__pattern": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnLctnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-DstrctNm__maxLength": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-DstrctNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-DstrctNm__pattern": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-DstrctNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-CtrySubDvsn__maxLength": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-CtrySubDvsn darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-CtrySubDvsn__pattern": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-CtrySubDvsn muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Ctry__pattern": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Ctry muss dem Muster ^[A-Z]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-AdrLine__maxItems": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-AdrLine darf höchstens 3 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-AdrLine__maxLength": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-AdrLine darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-AdrLine__pattern": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-AdrLine muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-BrnchId-Id__maxLength": "CdtTrfTxInf-CdtrAgt-BrnchId-Id darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-BrnchId-Id__pattern": "CdtTrfTxInf-CdtrAgt-BrnchId-Id muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-IBAN__pattern": "CdtTrfTxInf-CdtrAgtAcct-Id-IBAN muss dem Muster ^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Id__maxLength": "CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Id darf höchstens 34 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Id__pattern": "CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Id muss dem Muster ^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-Cd__maxLength": "CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-Prtry__maxLength": "CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-Prtry__pattern": "CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Issr__maxLength": "CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Issr darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Issr__pattern": "CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Issr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Tp-Cd__maxLength": "CdtTrfTxInf-CdtrAgtAcct-Tp-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Tp-Prtry__maxLength": "CdtTrfTxInf-CdtrAgtAcct-Tp-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Tp-Prtry__pattern": "CdtTrfTxInf-CdtrAgtAcct-Tp-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Ccy__pattern": "CdtTrfTxInf-CdtrAgtAcct-Ccy muss dem Muster ^[A-Z]{3,3}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Nm__maxLength": "CdtTrfTxInf-CdtrAgtAcct-Nm darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Nm__pattern": "CdtTrfTxInf-CdtrAgtAcct-Nm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Cd__maxLength": "CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Prtry__maxLength": "CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Prtry__pattern": "CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Id__maxLength": "CdtTrfTxInf-CdtrAgtAcct-Prxy-Id darf höchstens 320 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Id__pattern": "CdtTrfTxInf-CdtrAgtAcct-Prxy-Id muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt__maxItems": "CdtTrfTxInf-InstrForCdtrAgt darf höchstens 2 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd__pattern": "CdtTrfTxInf-InstrForCdtrAgt-Cd muss einer der folgenden Werte sein: CHQB, HOLD, PHOB, TELB.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-InstrInf__maxLength": "CdtTrfTxInf-InstrForCdtrAgt-InstrInf darf höchstens 140 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-InstrInf__pattern": "CdtTrfTxInf-InstrForCdtrAgt-InstrInf muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForNxtAgt__maxItems": "CdtTrfTxInf-InstrForNxtAgt darf höchstens 6 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForNxtAgt-InstrInf__maxLength": "CdtTrfTxInf-InstrForNxtAgt-InstrInf darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForNxtAgt-InstrInf__pattern": "CdtTrfTxInf-InstrForNxtAgt-InstrInf muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId__conditional-required__724d5260+FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd__required-conditional": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd ist erforderlich, wenn CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId__conditional-required__1f73110c+FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId__required-conditional": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId ist erforderlich, wenn CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Id__conditional-required__734825e+FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Id__required-conditional": "CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Id ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-(SchmeNm oder Issr) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Id__conditional-required__f48c5fc3+FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Id__required-conditional": "CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Id ist erforderlich, wenn CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id__conditional-required__fe5a162a+FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-IBAN__required-conditional": "CdtTrfTxInf-PrvsInstgAgt1Acct-Id-IBAN ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-PrvsInstgAgt1Acct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id__conditional-required__fe5a162a+FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Id__required-conditional": "CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Id ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-PrvsInstgAgt1Acct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId__conditional-required__e773a003+FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd__required-conditional": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd ist erforderlich, wenn CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId__conditional-required__3ac4be2f+FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId__required-conditional": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId ist erforderlich, wenn CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Id__conditional-required__1a0d0bd+FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Id__required-conditional": "CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Id ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-(SchmeNm oder Issr) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Id__conditional-required__9c211fe3+FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Id__required-conditional": "CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Id ist erforderlich, wenn CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id__conditional-required__5b5c2949+FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-IBAN__required-conditional": "CdtTrfTxInf-PrvsInstgAgt2Acct-Id-IBAN ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-PrvsInstgAgt2Acct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id__conditional-required__5b5c2949+FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Id__required-conditional": "CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Id ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-PrvsInstgAgt2Acct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId__conditional-required__6480c1a2+FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd__required-conditional": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd ist erforderlich, wenn CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId__conditional-required__a4db694e+FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId__required-conditional": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId ist erforderlich, wenn CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Id__conditional-required__46489e9c+FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Id__required-conditional": "CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Id ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-(SchmeNm oder Issr) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Id__conditional-required__d9147a83+FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Id__required-conditional": "CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Id ist erforderlich, wenn CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id__conditional-required__40e55de8+FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-IBAN__required-conditional": "CdtTrfTxInf-PrvsInstgAgt3Acct-Id-IBAN ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-PrvsInstgAgt3Acct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id__conditional-required__40e55de8+FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Id__required-conditional": "CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Id ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-PrvsInstgAgt3Acct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId__conditional-required__3f6a8bf6+FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd__required-conditional": "CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd ist erforderlich, wenn CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId__conditional-required__6a1dea1a+FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId__required-conditional": "CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId ist erforderlich, wenn CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId__conditional-required__f6eaf4f5+FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd__required-conditional": "CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd ist erforderlich, wenn CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId__conditional-required__93fb7719+FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId__required-conditional": "CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId ist erforderlich, wenn CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId__conditional-required__b37ddf5+FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd__required-conditional": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd ist erforderlich, wenn CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId__conditional-required__72932019+FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId__required-conditional": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId ist erforderlich, wenn CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Id__conditional-required__9e5b4d8b+FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Id__required-conditional": "CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Id ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-(SchmeNm oder Issr) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Id__conditional-required__de967603+FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Id__required-conditional": "CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Id ist erforderlich, wenn CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id__conditional-required__b92b393f+FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-IBAN__required-conditional": "CdtTrfTxInf-IntrmyAgt1Acct-Id-IBAN ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-IntrmyAgt1Acct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id__conditional-required__b92b393f+FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Id__required-conditional": "CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Id ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-IntrmyAgt1Acct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId__conditional-required__b81d8e56+FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd__required-conditional": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd ist erforderlich, wenn CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId__conditional-required__bec626fa+FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId__required-conditional": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId ist erforderlich, wenn CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Id__conditional-required__3b6d85c8+FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Id__required-conditional": "CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Id ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-(SchmeNm oder Issr) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Id__conditional-required__9a5363c3+FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Id__required-conditional": "CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Id ist erforderlich, wenn CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id__conditional-required__d48e6ffc+FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-IBAN__required-conditional": "CdtTrfTxInf-IntrmyAgt2Acct-Id-IBAN ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-IntrmyAgt2Acct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id__conditional-required__d48e6ffc+FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Id__required-conditional": "CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Id ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-IntrmyAgt2Acct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId__conditional-required__a341ce37+FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd__required-conditional": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd ist erforderlich, wenn CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId__conditional-required__c122f95b+FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId__required-conditional": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId ist erforderlich, wenn CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Id__conditional-required__a594f109+FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Id__required-conditional": "CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Id ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-(SchmeNm oder Issr) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Id__conditional-required__3c649703+FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Id__required-conditional": "CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Id ist erforderlich, wenn CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id__conditional-required__7aed1bbd+FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-IBAN__required-conditional": "CdtTrfTxInf-IntrmyAgt3Acct-Id-IBAN ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-IntrmyAgt3Acct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id__conditional-required__7aed1bbd+FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Id__required-conditional": "CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Id ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-IntrmyAgt3Acct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-TwnNm__conditional-required__47184d66+FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-TwnNm__required-conditional": "CdtTrfTxInf-InitgPty-PstlAdr-TwnNm ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-InitgPty-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnLctnNm oder DstrctNm oder CtrySubDvsn oder Ctry oder AdrLine) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-Ctry__conditional-required__9e8f2ff4+FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-Ctry__required-conditional": "CdtTrfTxInf-InitgPty-PstlAdr-Ctry ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-InitgPty-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnNm oder TwnLctnNm oder DstrctNm oder CtrySubDvsn oder AdrLine) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-Id__conditional-required__9d99b532+FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-Id__required-conditional": "CdtTrfTxInf-InitgPty-Id-OrgId-Othr-Id ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-InitgPty-Id-OrgId-Othr-(SchmeNm oder Issr) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-BirthDt__conditional-required__6614dae0+FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-BirthDt__required-conditional": "CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-BirthDt ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-(PrvcOfBirth oder CityOfBirth oder CtryOfBirth) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth__conditional-required__ee886e9e+FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth__required-conditional": "CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-(BirthDt oder PrvcOfBirth oder CtryOfBirth) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth__conditional-required__ad023885+FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth__required-conditional": "CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-(BirthDt oder PrvcOfBirth oder CityOfBirth) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-Id__conditional-required__e31b3708+FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-Id__required-conditional": "CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-Id ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-(SchmeNm oder Issr) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId__conditional-required__f8837ad1+FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd__required-conditional": "CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd ist erforderlich, wenn CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId__conditional-required__af3af2fd+FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId__required-conditional": "CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId ist erforderlich, wenn CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Id__conditional-required__8ef3a02f+FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Id__required-conditional": "CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Id ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-DbtrAgtAcct-Id-Othr-(SchmeNm oder Issr) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Id__conditional-required__538a6de3+FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Id__required-conditional": "CdtTrfTxInf-DbtrAgtAcct-Prxy-Id ist erforderlich, wenn CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id__conditional-required__a4e72e9b+FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-IBAN__required-conditional": "CdtTrfTxInf-DbtrAgtAcct-Id-IBAN ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-DbtrAgtAcct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id__conditional-required__a4e72e9b+FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Id__required-conditional": "CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Id ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-DbtrAgtAcct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId__conditional-required__297b90f0+FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd__required-conditional": "CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd ist erforderlich, wenn CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId__conditional-required__8ca3f39c+FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId__required-conditional": "CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId ist erforderlich, wenn CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Id__conditional-required__7b30878e+FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Id__required-conditional": "CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Id ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-CdtrAgtAcct-Id-Othr-(SchmeNm oder Issr) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Id__conditional-required__7290e2c3+FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Id__required-conditional": "CdtTrfTxInf-CdtrAgtAcct-Prxy-Id ist erforderlich, wenn CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id__conditional-required__1b4afaba+FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-IBAN__required-conditional": "CdtTrfTxInf-CdtrAgtAcct-Id-IBAN ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-CdtrAgtAcct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id__conditional-required__1b4afaba+FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Id__required-conditional": "CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Id ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-CdtrAgtAcct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.",
    "rule.R12__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Ctry__conditional-required__7e2b02f6+FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Ctry__required-conditional": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Ctry ist erforderlich, wenn CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnNm gesetzt ist. (R12)",
    "rule.R12__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-[AdrLine_TwnNm]__conditional-required__28b43e7a+FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-AdrLine__required-conditional": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-(AdrLine oder TwnNm) ist erforderlich, wenn CdtTrfTxInf-CdtrAgt-FinInstnId-Nm gesetzt ist. (R12)",
    "rule.R12__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-[AdrLine_TwnNm]__conditional-required__28b43e7a+FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnNm__required-conditional": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-(TwnNm oder AdrLine) ist erforderlich, wenn CdtTrfTxInf-CdtrAgt-FinInstnId-Nm gesetzt ist. (R12)",
    "rule.R12__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-Nm__conditional-required__581a5fa9+FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-Nm__required-conditional": "CdtTrfTxInf-CdtrAgt-FinInstnId-Nm ist erforderlich, wenn CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr vorhanden ist. (R12)",
    "rule.R13__FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd__conditional-value__84722f58+FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd__value-conditional": "CdtTrfTxInf-InstrForCdtrAgt-Cd muss ungleich \"HOLD\" sein, wenn CdtTrfTxInf-InstrForCdtrAgt-Cd den Wert \"CHQB\" hat. (R13)",
    "rule.R14__FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd__conditional-value__84722f58+FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd__value-conditional": "CdtTrfTxInf-InstrForCdtrAgt-Cd muss ungleich \"TELB\" sein, wenn CdtTrfTxInf-InstrForCdtrAgt-Cd den Wert \"PHOB\" hat. (R14)",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtId__maxLength": "CdtTrfTxInf-RltdRmtInf-RmtId darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtId__pattern": "CdtTrfTxInf-RltdRmtInf-RmtId muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls__maxItems": "CdtTrfTxInf-RltdRmtInf-RmtLctnDtls darf höchstens 2 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-Mtd__pattern": "CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-Mtd muss einer der folgenden Werte sein: FAXI, EDIC, URID, EMAL, POST, SMSM.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-ElctrncAdr__maxLength": "CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-ElctrncAdr darf höchstens 2048 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-ElctrncAdr__pattern": "CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-ElctrncAdr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Nm__maxLength": "CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Nm darf höchstens 140 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Nm__pattern": "CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Nm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Dept__maxLength": "CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Dept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Dept__pattern": "CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Dept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-SubDept__maxLength": "CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-SubDept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-SubDept__pattern": "CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-SubDept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-StrtNm__maxLength": "CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-StrtNm darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-StrtNm__pattern": "CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-StrtNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-BldgNb__maxLength": "CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-BldgNb darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-BldgNb__pattern": "CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-BldgNb muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-BldgNm__maxLength": "CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-BldgNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-BldgNm__pattern": "CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-BldgNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Flr__maxLength": "CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Flr darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Flr__pattern": "CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Flr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-PstBx__maxLength": "CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-PstBx darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-PstBx__pattern": "CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-PstBx muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Room__maxLength": "CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Room darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Room__pattern": "CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Room muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-PstCd__maxLength": "CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-PstCd darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-PstCd__pattern": "CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-PstCd muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-TwnNm__maxLength": "CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-TwnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-TwnNm__pattern": "CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-TwnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-TwnLctnNm__maxLength": "CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-TwnLctnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-TwnLctnNm__pattern": "CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-TwnLctnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-DstrctNm__maxLength": "CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-DstrctNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-DstrctNm__pattern": "CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-DstrctNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-CtrySubDvsn__maxLength": "CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-CtrySubDvsn darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-CtrySubDvsn__pattern": "CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-CtrySubDvsn muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Ctry__pattern": "CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Ctry muss dem Muster ^[A-Z]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-AdrLine__maxItems": "CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-AdrLine darf höchstens 3 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-AdrLine__maxLength": "CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-AdrLine darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-AdrLine__pattern": "CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-AdrLine muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Ustrd__maxLength": "CdtTrfTxInf-RmtInf-Ustrd darf höchstens 140 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Ustrd__pattern": "CdtTrfTxInf-RmtInf-Ustrd muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry-Cd__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry-Cd muss einer der folgenden Werte sein: MSIN, CNFA, DNFA, CINV, CREN, DEBN, HIRI, SBIN, CMCN, SOAC, DISP, BOLD, VCHR, AROI, TSUT, PUOR.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry-Prtry__maxLength": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry-Prtry__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-Issr__maxLength": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-Issr darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-Issr__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-Issr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Nb__maxLength": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Nb darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Nb__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Nb muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-RltdDt__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-RltdDt muss dem Muster ^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)(?:Z|[+-][01]\\d:[0-5]\\d)?$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry-Cd__maxLength": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry-Prtry__maxLength": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry-Prtry__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-Issr__maxLength": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-Issr darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-Issr__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-Issr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Nb__maxLength": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Nb darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Nb__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Nb muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-RltdDt__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-RltdDt muss dem Muster ^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)(?:Z|[+-][01]\\d:[0-5]\\d)?$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Desc__maxLength": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Desc darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Desc__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Desc muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DuePyblAmt-Ccy__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DuePyblAmt-Ccy muss dem Muster ^[A-Z]{3,3}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DuePyblAmt-amount__maxLength": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DuePyblAmt-amount darf höchstens 19 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DuePyblAmt-amount__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DuePyblAmt-amount muss dem Muster ^0*(([0-9]{0,13}\\.[0-9]{1,5})|([0-9]{0,14}\\.[0-9]{1,4})|([0-9]{0,15}\\.[0-9]{1,3})|([0-9]{0,16}\\.[0-9]{1,2})|([0-9]{0,17}\\.[0-9]{1,1})|([0-9]{0,18}\\.)|0*|([0-9]{0,18}))$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Tp-Cd__maxLength": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Tp-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Tp-Prtry__maxLength": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Tp-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Tp-Prtry__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Tp-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Amt-Ccy__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Amt-Ccy muss dem Muster ^[A-Z]{3,3}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Amt-amount__maxLength": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Amt-amount darf höchstens 19 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Amt-amount__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Amt-amount muss dem Muster ^0*(([0-9]{0,13}\\.[0-9]{1,5})|([0-9]{0,14}\\.[0-9]{1,4})|([0-9]{0,15}\\.[0-9]{1,3})|([0-9]{0,16}\\.[0-9]{1,2})|([0-9]{0,17}\\.[0-9]{1,1})|([0-9]{0,18}\\.)|0*|([0-9]{0,18}))$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-CdtNoteAmt-Ccy__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-CdtNoteAmt-Ccy muss dem Muster ^[A-Z]{3,3}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-CdtNoteAmt-amount__maxLength": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-CdtNoteAmt-amount darf höchstens 19 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-CdtNoteAmt-amount__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-CdtNoteAmt-amount muss dem Muster ^0*(([0-9]{0,13}\\.[0-9]{1,5})|([0-9]{0,14}\\.[0-9]{1,4})|([0-9]{0,15}\\.[0-9]{1,3})|([0-9]{0,16}\\.[0-9]{1,2})|([0-9]{0,17}\\.[0-9]{1,1})|([0-9]{0,18}\\.)|0*|([0-9]{0,18}))$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Tp-Cd__maxLength": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Tp-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Tp-Prtry__maxLength": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Tp-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Tp-Prtry__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Tp-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Amt-Ccy__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Amt-Ccy muss dem Muster ^[A-Z]{3,3}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Amt-amount__maxLength": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Amt-amount darf höchstens 19 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Amt-amount__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Amt-amount muss dem Muster ^0*(([0-9]{0,13}\\.[0-9]{1,5})|([0-9]{0,14}\\.[0-9]{1,4})|([0-9]{0,15}\\.[0-9]{1,3})|([0-9]{0,16}\\.[0-9]{1,2})|([0-9]{0,17}\\.[0-9]{1,1})|([0-9]{0,18}\\.)|0*|([0-9]{0,18}))$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Amt-Ccy__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Amt-Ccy muss dem Muster ^[A-Z]{3,3}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Amt-amount__maxLength": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Amt-amount darf höchstens 19 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Amt-amount__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Amt-amount muss dem Muster ^0*(([0-9]{0,13}\\.[0-9]{1,5})|([0-9]{0,14}\\.[0-9]{1,4})|([0-9]{0,15}\\.[0-9]{1,3})|([0-9]{0,16}\\.[0-9]{1,2})|([0-9]{0,17}\\.[0-9]{1,1})|([0-9]{0,18}\\.)|0*|([0-9]{0,18}))$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-CdtDbtInd__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-CdtDbtInd muss einer der folgenden Werte sein: CRDT, DBIT.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Rsn__maxLength": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Rsn darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Rsn__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Rsn muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-AddtlInf__maxLength": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-AddtlInf darf höchstens 140 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-AddtlInf__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-AddtlInf muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-RmtdAmt-Ccy__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-RmtdAmt-Ccy muss dem Muster ^[A-Z]{3,3}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-RmtdAmt-amount__maxLength": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-RmtdAmt-amount darf höchstens 19 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-RmtdAmt-amount__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-RmtdAmt-amount muss dem Muster ^0*(([0-9]{0,13}\\.[0-9]{1,5})|([0-9]{0,14}\\.[0-9]{1,4})|([0-9]{0,15}\\.[0-9]{1,3})|([0-9]{0,16}\\.[0-9]{1,2})|([0-9]{0,17}\\.[0-9]{1,1})|([0-9]{0,18}\\.)|0*|([0-9]{0,18}))$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DuePyblAmt-Ccy__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DuePyblAmt-Ccy muss dem Muster ^[A-Z]{3,3}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DuePyblAmt-amount__maxLength": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DuePyblAmt-amount darf höchstens 19 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DuePyblAmt-amount__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DuePyblAmt-amount muss dem Muster ^0*(([0-9]{0,13}\\.[0-9]{1,5})|([0-9]{0,14}\\.[0-9]{1,4})|([0-9]{0,15}\\.[0-9]{1,3})|([0-9]{0,16}\\.[0-9]{1,2})|([0-9]{0,17}\\.[0-9]{1,1})|([0-9]{0,18}\\.)|0*|([0-9]{0,18}))$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Tp-Cd__maxLength": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Tp-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Tp-Prtry__maxLength": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Tp-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Tp-Prtry__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Tp-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Amt-Ccy__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Amt-Ccy muss dem Muster ^[A-Z]{3,3}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Amt-amount__maxLength": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Amt-amount darf höchstens 19 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Amt-amount__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Amt-amount muss dem Muster ^0*(([0-9]{0,13}\\.[0-9]{1,5})|([0-9]{0,14}\\.[0-9]{1,4})|([0-9]{0,15}\\.[0-9]{1,3})|([0-9]{0,16}\\.[0-9]{1,2})|([0-9]{0,17}\\.[0-9]{1,1})|([0-9]{0,18}\\.)|0*|([0-9]{0,18}))$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-CdtNoteAmt-Ccy__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-CdtNoteAmt-Ccy muss dem Muster ^[A-Z]{3,3}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-CdtNoteAmt-amount__maxLength": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-CdtNoteAmt-amount darf höchstens 19 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-CdtNoteAmt-amount__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-CdtNoteAmt-amount muss dem Muster ^0*(([0-9]{0,13}\\.[0-9]{1,5})|([0-9]{0,14}\\.[0-9]{1,4})|([0-9]{0,15}\\.[0-9]{1,3})|([0-9]{0,16}\\.[0-9]{1,2})|([0-9]{0,17}\\.[0-9]{1,1})|([0-9]{0,18}\\.)|0*|([0-9]{0,18}))$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Tp-Cd__maxLength": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Tp-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Tp-Prtry__maxLength": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Tp-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Tp-Prtry__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Tp-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Amt-Ccy__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Amt-Ccy muss dem Muster ^[A-Z]{3,3}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Amt-amount__maxLength": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Amt-amount darf höchstens 19 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Amt-amount__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Amt-amount muss dem Muster ^0*(([0-9]{0,13}\\.[0-9]{1,5})|([0-9]{0,14}\\.[0-9]{1,4})|([0-9]{0,15}\\.[0-9]{1,3})|([0-9]{0,16}\\.[0-9]{1,2})|([0-9]{0,17}\\.[0-9]{1,1})|([0-9]{0,18}\\.)|0*|([0-9]{0,18}))$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Amt-Ccy__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Amt-Ccy muss dem Muster ^[A-Z]{3,3}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Amt-amount__maxLength": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Amt-amount darf höchstens 19 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Amt-amount__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Amt-amount muss dem Muster ^0*(([0-9]{0,13}\\.[0-9]{1,5})|([0-9]{0,14}\\.[0-9]{1,4})|([0-9]{0,15}\\.[0-9]{1,3})|([0-9]{0,16}\\.[0-9]{1,2})|([0-9]{0,17}\\.[0-9]{1,1})|([0-9]{0,18}\\.)|0*|([0-9]{0,18}))$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-CdtDbtInd__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-CdtDbtInd muss einer der folgenden Werte sein: CRDT, DBIT.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Rsn__maxLength": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Rsn darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Rsn__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Rsn muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-AddtlInf__maxLength": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-AddtlInf darf höchstens 140 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-AddtlInf__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-AddtlInf muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-RmtdAmt-Ccy__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-RmtdAmt-Ccy muss dem Muster ^[A-Z]{3,3}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-RmtdAmt-amount__maxLength": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-RmtdAmt-amount darf höchstens 19 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-RmtdAmt-amount__pattern": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-RmtdAmt-amount muss dem Muster ^0*(([0-9]{0,13}\\.[0-9]{1,5})|([0-9]{0,14}\\.[0-9]{1,4})|([0-9]{0,15}\\.[0-9]{1,3})|([0-9]{0,16}\\.[0-9]{1,2})|([0-9]{0,17}\\.[0-9]{1,1})|([0-9]{0,18}\\.)|0*|([0-9]{0,18}))$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry-Cd__pattern": "CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry-Cd muss einer der folgenden Werte sein: RADM, RPIN, FXDR, DISP, PUOR, SCOR.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry-Prtry__maxLength": "CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry-Prtry__pattern": "CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-Issr__maxLength": "CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-Issr darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-Issr__pattern": "CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-Issr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Ref__maxLength": "CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Ref darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Ref__pattern": "CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Ref muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Nm__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcr-Nm darf höchstens 140 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Nm__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcr-Nm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-Dept__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-Dept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-Dept__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-Dept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-SubDept__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-SubDept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-SubDept__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-SubDept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-StrtNm__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-StrtNm darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-StrtNm__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-StrtNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-BldgNb__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-BldgNb darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-BldgNb__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-BldgNb muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-BldgNm__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-BldgNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-BldgNm__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-BldgNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-Flr__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-Flr darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-Flr__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-Flr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-PstBx__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-PstBx darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-PstBx__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-PstBx muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-Room__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-Room darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-Room__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-Room muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-PstCd__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-PstCd darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-PstCd__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-PstCd muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-TwnNm__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-TwnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-TwnNm__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-TwnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-TwnLctnNm__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-TwnLctnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-TwnLctnNm__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-TwnLctnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-DstrctNm__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-DstrctNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-DstrctNm__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-DstrctNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-CtrySubDvsn__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-CtrySubDvsn darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-CtrySubDvsn__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-CtrySubDvsn muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-Ctry__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-Ctry muss dem Muster ^[A-Z]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-AdrLine__maxItems": "CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-AdrLine darf höchstens 2 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-AdrLine__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-AdrLine darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-AdrLine__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-AdrLine muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-AnyBIC__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-AnyBIC muss dem Muster ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-LEI__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-LEI muss dem Muster ^[A-Z0-9]{18,18}[0-9]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr__maxItems": "CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr darf höchstens 2 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-Id__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-Id darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-Id__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-Id muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-SchmeNm-Cd__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-SchmeNm-Prtry__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-SchmeNm-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-SchmeNm-Prtry__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-SchmeNm-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-Issr__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-Issr darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-Issr__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-Issr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-BirthDt__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-BirthDt muss dem Muster ^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)(?:Z|[+-][01]\\d:[0-5]\\d)?$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth muss dem Muster ^[A-Z]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr__maxItems": "CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr darf höchstens 2 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-Id__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-Id darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-Id__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-Id muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-SchmeNm-Cd__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-SchmeNm-Prtry__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-SchmeNm-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-SchmeNm-Prtry__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-SchmeNm-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-Issr__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-Issr darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-Issr__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-Issr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-CtryOfRes__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcr-CtryOfRes muss dem Muster ^[A-Z]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Nm__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcee-Nm darf höchstens 140 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Nm__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcee-Nm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Dept__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Dept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Dept__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Dept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-SubDept__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-SubDept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-SubDept__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-SubDept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-StrtNm__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-StrtNm darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-StrtNm__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-StrtNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-BldgNb__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-BldgNb darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-BldgNb__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-BldgNb muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-BldgNm__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-BldgNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-BldgNm__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-BldgNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Flr__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Flr darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Flr__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Flr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-PstBx__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-PstBx darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-PstBx__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-PstBx muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Room__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Room darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Room__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Room muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-PstCd__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-PstCd darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-PstCd__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-PstCd muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-TwnNm__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-TwnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-TwnNm__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-TwnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-TwnLctnNm__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-TwnLctnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-TwnLctnNm__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-TwnLctnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-DstrctNm__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-DstrctNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-DstrctNm__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-DstrctNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-CtrySubDvsn__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-CtrySubDvsn darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-CtrySubDvsn__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-CtrySubDvsn muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Ctry__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Ctry muss dem Muster ^[A-Z]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-AdrLine__maxItems": "CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-AdrLine darf höchstens 2 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-AdrLine__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-AdrLine darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-AdrLine__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-AdrLine muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-AnyBIC__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-AnyBIC muss dem Muster ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-LEI__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-LEI muss dem Muster ^[A-Z0-9]{18,18}[0-9]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr__maxItems": "CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr darf höchstens 2 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-Id__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-Id darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-Id__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-Id muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-SchmeNm-Cd__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-SchmeNm-Prtry__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-SchmeNm-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-SchmeNm-Prtry__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-SchmeNm-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-Issr__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-Issr darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-Issr__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-Issr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-BirthDt__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-BirthDt muss dem Muster ^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)(?:Z|[+-][01]\\d:[0-5]\\d)?$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth muss dem Muster ^[A-Z]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr__maxItems": "CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr darf höchstens 2 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-Id__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-Id darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-Id__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-Id muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-SchmeNm-Cd__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-SchmeNm-Prtry__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-SchmeNm-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-SchmeNm-Prtry__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-SchmeNm-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-Issr__maxLength": "CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-Issr darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-Issr__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-Issr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-CtryOfRes__pattern": "CdtTrfTxInf-RmtInf-Strd-Invcee-CtryOfRes muss dem Muster ^[A-Z]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr-TaxId__maxLength": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr-TaxId darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr-TaxId__pattern": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr-TaxId muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr-RegnId__maxLength": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr-RegnId darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr-RegnId__pattern": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr-RegnId muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr-TaxTp__maxLength": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr-TaxTp darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr-TaxTp__pattern": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr-TaxTp muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-TaxId__maxLength": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-TaxId darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-TaxId__pattern": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-TaxId muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-RegnId__maxLength": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-RegnId darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-RegnId__pattern": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-RegnId muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-TaxTp__maxLength": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-TaxTp darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-TaxTp__pattern": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-TaxTp muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-Authstn-Titl__maxLength": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-Authstn-Titl darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-Authstn-Titl__pattern": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-Authstn-Titl muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-Authstn-Nm__maxLength": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-Authstn-Nm darf höchstens 140 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-Authstn-Nm__pattern": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-Authstn-Nm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-TaxId__maxLength": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-TaxId darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-TaxId__pattern": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-TaxId muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-RegnId__maxLength": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-RegnId darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-RegnId__pattern": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-RegnId muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-TaxTp__maxLength": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-TaxTp darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-TaxTp__pattern": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-TaxTp muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-Authstn-Titl__maxLength": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-Authstn-Titl darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-Authstn-Titl__pattern": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-Authstn-Titl muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-Authstn-Nm__maxLength": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-Authstn-Nm darf höchstens 140 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-Authstn-Nm__pattern": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-Authstn-Nm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-AdmstnZone__maxLength": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-AdmstnZone darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-AdmstnZone__pattern": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-AdmstnZone muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-RefNb__maxLength": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-RefNb darf höchstens 140 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-RefNb__pattern": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-RefNb muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Mtd__maxLength": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Mtd darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Mtd__pattern": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Mtd muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxblBaseAmt-Ccy__pattern": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxblBaseAmt-Ccy muss dem Muster ^[A-Z]{3,3}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxblBaseAmt-amount__maxLength": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxblBaseAmt-amount darf höchstens 19 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxblBaseAmt-amount__pattern": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxblBaseAmt-amount muss dem Muster ^0*(([0-9]{0,13}\\.[0-9]{1,5})|([0-9]{0,14}\\.[0-9]{1,4})|([0-9]{0,15}\\.[0-9]{1,3})|([0-9]{0,16}\\.[0-9]{1,2})|([0-9]{0,17}\\.[0-9]{1,1})|([0-9]{0,18}\\.)|0*|([0-9]{0,18}))$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxAmt-Ccy__pattern": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxAmt-Ccy muss dem Muster ^[A-Z]{3,3}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxAmt-amount__maxLength": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxAmt-amount darf höchstens 19 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxAmt-amount__pattern": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxAmt-amount muss dem Muster ^0*(([0-9]{0,13}\\.[0-9]{1,5})|([0-9]{0,14}\\.[0-9]{1,4})|([0-9]{0,15}\\.[0-9]{1,3})|([0-9]{0,16}\\.[0-9]{1,2})|([0-9]{0,17}\\.[0-9]{1,1})|([0-9]{0,18}\\.)|0*|([0-9]{0,18}))$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dt__pattern": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dt muss dem Muster ^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)(?:Z|[+-][01]\\d:[0-5]\\d)?$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-SeqNb__maxLength": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-SeqNb darf höchstens 19 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Tp__maxLength": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Tp darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Tp__pattern": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Tp muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Ctgy__maxLength": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Ctgy darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Ctgy__pattern": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Ctgy muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-CtgyDtls__maxLength": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-CtgyDtls darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-CtgyDtls__pattern": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-CtgyDtls muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-DbtrSts__maxLength": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-DbtrSts darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-DbtrSts__pattern": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-DbtrSts muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-CertId__maxLength": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-CertId darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-CertId__pattern": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-CertId muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-FrmsCd__maxLength": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-FrmsCd darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-FrmsCd__pattern": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-FrmsCd muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-Yr__pattern": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-Yr muss dem Muster ^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)(?:Z|[+-][01]\\d:[0-5]\\d)?$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-Tp__pattern": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-Tp muss einer der folgenden Werte sein: MM01, MM02, MM03, MM04, MM05, MM06, MM07, MM08, MM09, MM10, MM11, MM12, QTR1, QTR2, QTR3, QTR4, HLF1, HLF2.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-FrToDt-FrDt__pattern": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-FrToDt-FrDt muss dem Muster ^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)(?:Z|[+-][01]\\d:[0-5]\\d)?$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-FrToDt-ToDt__pattern": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-FrToDt-ToDt muss dem Muster ^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)(?:Z|[+-][01]\\d:[0-5]\\d)?$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Rate__maxLength": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Rate darf höchstens 12 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TaxblBaseAmt-Ccy__pattern": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TaxblBaseAmt-Ccy muss dem Muster ^[A-Z]{3,3}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TaxblBaseAmt-amount__maxLength": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TaxblBaseAmt-amount darf höchstens 19 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TaxblBaseAmt-amount__pattern": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TaxblBaseAmt-amount muss dem Muster ^0*(([0-9]{0,13}\\.[0-9]{1,5})|([0-9]{0,14}\\.[0-9]{1,4})|([0-9]{0,15}\\.[0-9]{1,3})|([0-9]{0,16}\\.[0-9]{1,2})|([0-9]{0,17}\\.[0-9]{1,1})|([0-9]{0,18}\\.)|0*|([0-9]{0,18}))$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TtlAmt-Ccy__pattern": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TtlAmt-Ccy muss dem Muster ^[A-Z]{3,3}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TtlAmt-amount__maxLength": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TtlAmt-amount darf höchstens 19 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TtlAmt-amount__pattern": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TtlAmt-amount muss dem Muster ^0*(([0-9]{0,13}\\.[0-9]{1,5})|([0-9]{0,14}\\.[0-9]{1,4})|([0-9]{0,15}\\.[0-9]{1,3})|([0-9]{0,16}\\.[0-9]{1,2})|([0-9]{0,17}\\.[0-9]{1,1})|([0-9]{0,18}\\.)|0*|([0-9]{0,18}))$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-Yr__pattern": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-Yr muss dem Muster ^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)(?:Z|[+-][01]\\d:[0-5]\\d)?$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-Tp__pattern": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-Tp muss einer der folgenden Werte sein: MM01, MM02, MM03, MM04, MM05, MM06, MM07, MM08, MM09, MM10, MM11, MM12, QTR1, QTR2, QTR3, QTR4, HLF1, HLF2.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-FrToDt-FrDt__pattern": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-FrToDt-FrDt muss dem Muster ^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)(?:Z|[+-][01]\\d:[0-5]\\d)?$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-FrToDt-ToDt__pattern": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-FrToDt-ToDt muss dem Muster ^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)(?:Z|[+-][01]\\d:[0-5]\\d)?$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt-Ccy__pattern": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt-Ccy muss dem Muster ^[A-Z]{3,3}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt-amount__maxLength": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt-amount darf höchstens 19 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt-amount__pattern": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt-amount muss dem Muster ^0*(([0-9]{0,13}\\.[0-9]{1,5})|([0-9]{0,14}\\.[0-9]{1,4})|([0-9]{0,15}\\.[0-9]{1,3})|([0-9]{0,16}\\.[0-9]{1,2})|([0-9]{0,17}\\.[0-9]{1,1})|([0-9]{0,18}\\.)|0*|([0-9]{0,18}))$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-AddtlInf__maxLength": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-AddtlInf darf höchstens 140 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-AddtlInf__pattern": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-AddtlInf muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry-Cd__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry-Prtry__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry-Prtry__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-Issr__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-Issr darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-Issr__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-Issr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Nm__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Nm darf höchstens 140 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Nm__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Nm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Dept__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Dept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Dept__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Dept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-SubDept__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-SubDept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-SubDept__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-SubDept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-StrtNm__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-StrtNm darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-StrtNm__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-StrtNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-BldgNb__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-BldgNb darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-BldgNb__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-BldgNb muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-BldgNm__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-BldgNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-BldgNm__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-BldgNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Flr__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Flr darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Flr__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Flr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-PstBx__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-PstBx darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-PstBx__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-PstBx muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Room__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Room darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Room__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Room muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-PstCd__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-PstCd darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-PstCd__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-PstCd muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-TwnNm__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-TwnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-TwnNm__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-TwnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-TwnLctnNm__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-TwnLctnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-TwnLctnNm__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-TwnLctnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-DstrctNm__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-DstrctNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-DstrctNm__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-DstrctNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-CtrySubDvsn__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-CtrySubDvsn darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-CtrySubDvsn__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-CtrySubDvsn muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Ctry__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Ctry muss dem Muster ^[A-Z]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-AdrLine__maxItems": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-AdrLine darf höchstens 2 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-AdrLine__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-AdrLine darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-AdrLine__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-AdrLine muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-AnyBIC__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-AnyBIC muss dem Muster ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-LEI__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-LEI muss dem Muster ^[A-Z0-9]{18,18}[0-9]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr__maxItems": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr darf höchstens 2 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-Id__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-Id darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-Id__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-Id muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-SchmeNm-Cd__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-SchmeNm-Prtry__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-SchmeNm-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-SchmeNm-Prtry__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-SchmeNm-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-Issr__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-Issr darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-Issr__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-Issr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-BirthDt__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-BirthDt muss dem Muster ^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)(?:Z|[+-][01]\\d:[0-5]\\d)?$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth muss dem Muster ^[A-Z]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr__maxItems": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr darf höchstens 2 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-Id__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-Id darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-Id__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-Id muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-SchmeNm-Cd__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-SchmeNm-Prtry__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-SchmeNm-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-SchmeNm-Prtry__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-SchmeNm-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-Issr__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-Issr darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-Issr__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-Issr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-CtryOfRes__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-CtryOfRes muss dem Muster ^[A-Z]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Nm__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Nm darf höchstens 140 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Nm__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Nm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Dept__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Dept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Dept__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Dept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-SubDept__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-SubDept darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-SubDept__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-SubDept muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-StrtNm__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-StrtNm darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-StrtNm__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-StrtNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-BldgNb__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-BldgNb darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-BldgNb__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-BldgNb muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-BldgNm__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-BldgNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-BldgNm__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-BldgNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Flr__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Flr darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Flr__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Flr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-PstBx__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-PstBx darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-PstBx__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-PstBx muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Room__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Room darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Room__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Room muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-PstCd__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-PstCd darf höchstens 16 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-PstCd__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-PstCd muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-TwnNm__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-TwnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-TwnNm__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-TwnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-TwnLctnNm__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-TwnLctnNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-TwnLctnNm__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-TwnLctnNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-DstrctNm__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-DstrctNm darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-DstrctNm__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-DstrctNm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-CtrySubDvsn__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-CtrySubDvsn darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-CtrySubDvsn__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-CtrySubDvsn muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Ctry__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Ctry muss dem Muster ^[A-Z]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-AdrLine__maxItems": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-AdrLine darf höchstens 2 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-AdrLine__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-AdrLine darf höchstens 70 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-AdrLine__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-AdrLine muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-AnyBIC__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-AnyBIC muss dem Muster ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-LEI__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-LEI muss dem Muster ^[A-Z0-9]{18,18}[0-9]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr__maxItems": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr darf höchstens 2 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-Id__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-Id darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-Id__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-Id muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-SchmeNm-Cd__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-SchmeNm-Prtry__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-SchmeNm-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-SchmeNm-Prtry__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-SchmeNm-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-Issr__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-Issr darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-Issr__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-Issr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-BirthDt__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-BirthDt muss dem Muster ^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)(?:Z|[+-][01]\\d:[0-5]\\d)?$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth muss dem Muster ^[A-Z]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr__maxItems": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr darf höchstens 2 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-Id__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-Id darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-Id__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-Id muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-SchmeNm-Cd__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-SchmeNm-Prtry__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-SchmeNm-Prtry darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-SchmeNm-Prtry__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-SchmeNm-Prtry muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-Issr__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-Issr darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-Issr__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-Issr muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-CtryOfRes__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-CtryOfRes muss dem Muster ^[A-Z]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-RefNb__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-RefNb darf höchstens 140 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-RefNb__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-RefNb muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Dt__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Dt muss dem Muster ^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)(?:Z|[+-][01]\\d:[0-5]\\d)?$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-RmtdAmt-Ccy__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-RmtdAmt-Ccy muss dem Muster ^[A-Z]{3,3}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-RmtdAmt-amount__maxLength": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-RmtdAmt-amount darf höchstens 19 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-RmtdAmt-amount__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-RmtdAmt-amount muss dem Muster ^0*(([0-9]{0,13}\\.[0-9]{1,5})|([0-9]{0,14}\\.[0-9]{1,4})|([0-9]{0,15}\\.[0-9]{1,3})|([0-9]{0,16}\\.[0-9]{1,2})|([0-9]{0,17}\\.[0-9]{1,1})|([0-9]{0,18}\\.)|0*|([0-9]{0,18}))$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-FmlyMdclInsrncInd__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-FmlyMdclInsrncInd muss einer der folgenden Werte sein: true, false.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-MplyeeTermntnInd__pattern": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-MplyeeTermntnInd muss einer der folgenden Werte sein: true, false.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-AddtlRmtInf__maxItems": "CdtTrfTxInf-RmtInf-Strd-AddtlRmtInf darf höchstens 3 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-AddtlRmtInf__maxLength": "CdtTrfTxInf-RmtInf-Strd-AddtlRmtInf darf höchstens 140 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-AddtlRmtInf__pattern": "CdtTrfTxInf-RmtInf-Strd-AddtlRmtInf muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Nm__conditional-required__3c673537+FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Nm__required-conditional": "CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Nm ist erforderlich, wenn CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-Mtd__conditional-required__6f499703+FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-Mtd__required-conditional": "CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-Mtd ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-(ElctrncAdr oder PstlAdr) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry__conditional-required__13ce2d47+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry-Cd__required-conditional": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry-Cd ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-Issr vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry__conditional-required__13ce2d47+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry-Prtry__required-conditional": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry-Prtry ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-Issr vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry__conditional-required__ad4b22cb+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry-Cd__required-conditional": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry-Cd ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-Issr vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry__conditional-required__ad4b22cb+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry-Prtry__required-conditional": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry-Prtry ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-Issr vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DuePyblAmt-Ccy__conditional-required__d7ef6fe6+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DuePyblAmt-Ccy__required-conditional": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DuePyblAmt-Ccy ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DuePyblAmt-amount vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DuePyblAmt-amount__conditional-required__6fb68113+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DuePyblAmt-amount__required-conditional": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DuePyblAmt-amount ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DuePyblAmt-Ccy vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Amt-Ccy__conditional-required__9fe48917+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Amt-Ccy__required-conditional": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Amt-Ccy ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Amt-amount vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Amt-amount__conditional-required__87fec002+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Amt-amount__required-conditional": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Amt-amount ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Amt-Ccy vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Amt__conditional-required__18c54a63+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Amt-Ccy__required-conditional": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Amt-Ccy ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Tp vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Amt__conditional-required__18c54a63+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Amt-amount__required-conditional": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Amt-amount ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Tp vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-CdtNoteAmt-Ccy__conditional-required__b2c98e56+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-CdtNoteAmt-Ccy__required-conditional": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-CdtNoteAmt-Ccy ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-CdtNoteAmt-amount vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-CdtNoteAmt-amount__conditional-required__4c200d63+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-CdtNoteAmt-amount__required-conditional": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-CdtNoteAmt-amount ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-CdtNoteAmt-Ccy vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Amt-Ccy__conditional-required__ea12118d+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Amt-Ccy__required-conditional": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Amt-Ccy ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Amt-amount vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Amt-amount__conditional-required__aefa4558+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Amt-amount__required-conditional": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Amt-amount ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Amt-Ccy vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Amt__conditional-required__ee0b6b23+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Amt-Ccy__required-conditional": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Amt-Ccy ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Tp vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Amt__conditional-required__ee0b6b23+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Amt-amount__required-conditional": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Amt-amount ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Tp vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Amt-Ccy__conditional-required__8248197b+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Amt-Ccy__required-conditional": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Amt-Ccy ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Amt-amount vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Amt-amount__conditional-required__fef09bee+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Amt-amount__required-conditional": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Amt-amount ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Amt-Ccy vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Amt__conditional-required__a70d2277+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Amt-Ccy__required-conditional": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Amt-Ccy ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-(CdtDbtInd oder Rsn oder AddtlInf) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Amt__conditional-required__a70d2277+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Amt-amount__required-conditional": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Amt-amount ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-(CdtDbtInd oder Rsn oder AddtlInf) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-RmtdAmt-Ccy__conditional-required__6a0a50da+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-RmtdAmt-Ccy__required-conditional": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-RmtdAmt-Ccy ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-RmtdAmt-amount vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-RmtdAmt-amount__conditional-required__503ff6f+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-RmtdAmt-amount__required-conditional": "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-RmtdAmt-amount ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-RmtdAmt-Ccy vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DuePyblAmt-Ccy__conditional-required__2af6bcc6+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DuePyblAmt-Ccy__required-conditional": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DuePyblAmt-Ccy ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DuePyblAmt-amount vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DuePyblAmt-amount__conditional-required__4bf8c9f3+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DuePyblAmt-amount__required-conditional": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DuePyblAmt-amount ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DuePyblAmt-Ccy vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Amt-Ccy__conditional-required__b6ceedb7+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Amt-Ccy__required-conditional": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Amt-Ccy ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Amt-amount vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Amt-amount__conditional-required__a621d922+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Amt-amount__required-conditional": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Amt-amount ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Amt-Ccy vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Amt__conditional-required__6b70d843+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Amt-Ccy__required-conditional": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Amt-Ccy ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Tp vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Amt__conditional-required__6b70d843+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Amt-amount__required-conditional": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Amt-amount ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Tp vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-CdtNoteAmt-Ccy__conditional-required__ca9bcb36+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-CdtNoteAmt-Ccy__required-conditional": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-CdtNoteAmt-Ccy ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-CdtNoteAmt-amount vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-CdtNoteAmt-amount__conditional-required__b61d66c3+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-CdtNoteAmt-amount__required-conditional": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-CdtNoteAmt-amount ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-CdtNoteAmt-Ccy vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Amt-Ccy__conditional-required__94790f6d+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Amt-Ccy__required-conditional": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Amt-Ccy ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Amt-amount vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Amt-amount__conditional-required__a73a5f38+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Amt-amount__required-conditional": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Amt-amount ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Amt-Ccy vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Amt__conditional-required__5642b8c3+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Amt-Ccy__required-conditional": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Amt-Ccy ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Tp vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Amt__conditional-required__5642b8c3+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Amt-amount__required-conditional": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Amt-amount ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Tp vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Amt-Ccy__conditional-required__33cc049b+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Amt-Ccy__required-conditional": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Amt-Ccy ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Amt-amount vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Amt-amount__conditional-required__d35c0b0e+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Amt-amount__required-conditional": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Amt-amount ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Amt-Ccy vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Amt__conditional-required__4779fdf7+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Amt-Ccy__required-conditional": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Amt-Ccy ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-(CdtDbtInd oder Rsn oder AddtlInf) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Amt__conditional-required__4779fdf7+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Amt-amount__required-conditional": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Amt-amount ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-(CdtDbtInd oder Rsn oder AddtlInf) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-RmtdAmt-Ccy__conditional-required__5224113a+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-RmtdAmt-Ccy__required-conditional": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-RmtdAmt-Ccy ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-RmtdAmt-amount vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-RmtdAmt-amount__conditional-required__8f4b8c4f+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-RmtdAmt-amount__required-conditional": "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-RmtdAmt-amount ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-RmtdAmt-Ccy vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry__conditional-required__6807241d+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry-Cd__required-conditional": "CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry-Cd ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-Issr vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry__conditional-required__6807241d+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry-Prtry__required-conditional": "CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry-Prtry ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-Issr vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-TwnNm__conditional-required__225bff26+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-TwnNm__required-conditional": "CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-TwnNm ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnLctnNm oder DstrctNm oder CtrySubDvsn oder Ctry oder AdrLine) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-Ctry__conditional-required__41c59794+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-Ctry__required-conditional": "CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-Ctry ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnNm oder TwnLctnNm oder DstrctNm oder CtrySubDvsn oder AdrLine) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-Id__conditional-required__ed16df09+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-Id__required-conditional": "CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-Id ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-(SchmeNm oder Issr) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-BirthDt__conditional-required__76b57bbb+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-BirthDt__required-conditional": "CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-BirthDt ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-(PrvcOfBirth oder CityOfBirth oder CtryOfBirth) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth__conditional-required__a2a973c5+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth__required-conditional": "CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-(BirthDt oder PrvcOfBirth oder CtryOfBirth) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth__conditional-required__34d0413e+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth__required-conditional": "CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-(BirthDt oder PrvcOfBirth oder CityOfBirth) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-Id__conditional-required__af902933+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-Id__required-conditional": "CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-Id ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-(SchmeNm oder Issr) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-TwnNm__conditional-required__ce918026+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-TwnNm__required-conditional": "CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-TwnNm ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnLctnNm oder DstrctNm oder CtrySubDvsn oder Ctry oder AdrLine) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Ctry__conditional-required__96dd2574+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Ctry__required-conditional": "CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Ctry ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnNm oder TwnLctnNm oder DstrctNm oder CtrySubDvsn oder AdrLine) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-Id__conditional-required__e3c428fb+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-Id__required-conditional": "CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-Id ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-(SchmeNm oder Issr) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-BirthDt__conditional-required__b080b309+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-BirthDt__required-conditional": "CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-BirthDt ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-(PrvcOfBirth oder CityOfBirth oder CtryOfBirth) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth__conditional-required__ca7e3877+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth__required-conditional": "CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-(BirthDt oder PrvcOfBirth oder CtryOfBirth) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth__conditional-required__dda36ac+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth__required-conditional": "CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-(BirthDt oder PrvcOfBirth oder CityOfBirth) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-Id__conditional-required__e0eb7901+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-Id__required-conditional": "CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-Id ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-(SchmeNm oder Issr) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxblBaseAmt-Ccy__conditional-required__e10326fb+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxblBaseAmt-Ccy__required-conditional": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxblBaseAmt-Ccy ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxblBaseAmt-amount vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxblBaseAmt-amount__conditional-required__c54a996e+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxblBaseAmt-amount__required-conditional": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxblBaseAmt-amount ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxblBaseAmt-Ccy vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxAmt-Ccy__conditional-required__5c589aa0+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxAmt-Ccy__required-conditional": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxAmt-Ccy ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxAmt-amount vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxAmt-amount__conditional-required__21d5b495+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxAmt-amount__required-conditional": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxAmt-amount ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxAmt-Ccy vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-FrToDt-FrDt__conditional-required__96963300+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-FrToDt-FrDt__required-conditional": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-FrToDt-FrDt ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-FrToDt-ToDt vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-FrToDt-ToDt__conditional-required__968eef8f+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-FrToDt-ToDt__required-conditional": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-FrToDt-ToDt ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-FrToDt-FrDt vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TaxblBaseAmt-Ccy__conditional-required__4cff05a5+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TaxblBaseAmt-Ccy__required-conditional": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TaxblBaseAmt-Ccy ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TaxblBaseAmt-amount vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TaxblBaseAmt-amount__conditional-required__6bb1dff0+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TaxblBaseAmt-amount__required-conditional": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TaxblBaseAmt-amount ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TaxblBaseAmt-Ccy vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TtlAmt-Ccy__conditional-required__70c460df+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TtlAmt-Ccy__required-conditional": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TtlAmt-Ccy ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TtlAmt-amount vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TtlAmt-amount__conditional-required__6c100f4a+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TtlAmt-amount__required-conditional": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TtlAmt-amount ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TtlAmt-Ccy vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-FrToDt-FrDt__conditional-required__6d8804da+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-FrToDt-FrDt__required-conditional": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-FrToDt-FrDt ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-FrToDt-ToDt vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-FrToDt-ToDt__conditional-required__6d8f4fd5+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-FrToDt-ToDt__required-conditional": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-FrToDt-ToDt ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-FrToDt-FrDt vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt-Ccy__conditional-required__fc0ae971+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt-Ccy__required-conditional": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt-Ccy ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt-amount vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt-amount__conditional-required__8d099ca4+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt-amount__required-conditional": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt-amount ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt-Ccy vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt__conditional-required__40ecd8f9+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt-Ccy__required-conditional": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt-Ccy ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt__conditional-required__40ecd8f9+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt-amount__required-conditional": "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt-amount ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry__conditional-required__2bb2d25e+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry-Cd__required-conditional": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry-Cd ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-Issr vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry__conditional-required__2bb2d25e+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry-Prtry__required-conditional": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry-Prtry ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-Issr vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-TwnNm__conditional-required__35e10ba6+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-TwnNm__required-conditional": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-TwnNm ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnLctnNm oder DstrctNm oder CtrySubDvsn oder Ctry oder AdrLine) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Ctry__conditional-required__fdcd5ef4+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Ctry__required-conditional": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Ctry ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnNm oder TwnLctnNm oder DstrctNm oder CtrySubDvsn oder AdrLine) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-Id__conditional-required__6a537616+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-Id__required-conditional": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-Id ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-(SchmeNm oder Issr) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-BirthDt__conditional-required__38108d84+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-BirthDt__required-conditional": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-BirthDt ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-(PrvcOfBirth oder CityOfBirth oder CtryOfBirth) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth__conditional-required__482e2f7a+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth__required-conditional": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-(BirthDt oder PrvcOfBirth oder CtryOfBirth) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth__conditional-required__585aea61+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth__required-conditional": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-(BirthDt oder PrvcOfBirth oder CityOfBirth) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-Id__conditional-required__fbf8266c+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-Id__required-conditional": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-Id ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-(SchmeNm oder Issr) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-TwnNm__conditional-required__2d022026+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-TwnNm__required-conditional": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-TwnNm ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnLctnNm oder DstrctNm oder CtrySubDvsn oder Ctry oder AdrLine) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Ctry__conditional-required__e9c26174+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Ctry__required-conditional": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Ctry ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnNm oder TwnLctnNm oder DstrctNm oder CtrySubDvsn oder AdrLine) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-Id__conditional-required__7cff63f2+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-Id__required-conditional": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-Id ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-(SchmeNm oder Issr) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-BirthDt__conditional-required__5acdebe0+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-BirthDt__required-conditional": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-BirthDt ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-(PrvcOfBirth oder CityOfBirth oder CtryOfBirth) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth__conditional-required__6d85459e+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth__required-conditional": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-(BirthDt oder PrvcOfBirth oder CtryOfBirth) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth__conditional-required__71d9ad45+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth__required-conditional": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-(BirthDt oder PrvcOfBirth oder CityOfBirth) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-Id__conditional-required__f4ea9d08+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-Id__required-conditional": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-Id ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-(SchmeNm oder Issr) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-RmtdAmt-Ccy__conditional-required__b0e5865a+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-RmtdAmt-Ccy__required-conditional": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-RmtdAmt-Ccy ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-RmtdAmt-amount vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-RmtdAmt-amount__conditional-required__632f24ef+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-RmtdAmt-amount__required-conditional": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-RmtdAmt-amount ist erforderlich, wenn CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-RmtdAmt-Ccy vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry__conditional-required__38e9fb24+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry-Cd__required-conditional": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry-Cd ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-(Grnshee oder GrnshmtAdmstr oder RefNb oder Dt oder RmtdAmt oder FmlyMdclInsrncInd oder MplyeeTermntnInd) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry__conditional-required__38e9fb24+FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry-Prtry__required-conditional": "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry-Prtry ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-(Grnshee oder GrnshmtAdmstr oder RefNb oder Dt oder RmtdAmt oder FmlyMdclInsrncInd oder MplyeeTermntnInd) vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg__maxItems": "CdtTrfTxInf-RgltryRptg darf höchstens 10 Elemente haben.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-DbtCdtRptgInd__pattern": "CdtTrfTxInf-RgltryRptg-DbtCdtRptgInd muss einer der folgenden Werte sein: CRED, DEBT, BOTH.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Authrty-Nm__maxLength": "CdtTrfTxInf-RgltryRptg-Authrty-Nm darf höchstens 140 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Authrty-Nm__pattern": "CdtTrfTxInf-RgltryRptg-Authrty-Nm muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Authrty-Ctry__pattern": "CdtTrfTxInf-RgltryRptg-Authrty-Ctry muss dem Muster ^[A-Z]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Tp__maxLength": "CdtTrfTxInf-RgltryRptg-Dtls-Tp darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Tp__pattern": "CdtTrfTxInf-RgltryRptg-Dtls-Tp muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Dt__pattern": "CdtTrfTxInf-RgltryRptg-Dtls-Dt muss dem Muster ^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)(?:Z|[+-][01]\\d:[0-5]\\d)?$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Ctry__pattern": "CdtTrfTxInf-RgltryRptg-Dtls-Ctry muss dem Muster ^[A-Z]{2,2}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Cd__maxLength": "CdtTrfTxInf-RgltryRptg-Dtls-Cd darf höchstens 10 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Cd__pattern": "CdtTrfTxInf-RgltryRptg-Dtls-Cd muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Amt-Ccy__pattern": "CdtTrfTxInf-RgltryRptg-Dtls-Amt-Ccy muss dem Muster ^[A-Z]{3,3}$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Amt-amount__maxLength": "CdtTrfTxInf-RgltryRptg-Dtls-Amt-amount darf höchstens 15 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Amt-amount__pattern": "CdtTrfTxInf-RgltryRptg-Dtls-Amt-amount muss dem Muster ^0*(([0-9]{0,9}\\.[0-9]{1,5})|([0-9]{0,10}\\.[0-9]{1,4})|([0-9]{0,11}\\.[0-9]{1,3})|([0-9]{0,12}\\.[0-9]{1,2})|([0-9]{0,13}\\.[0-9]{1,1})|([0-9]{0,18}\\.)|0*|([0-9]{0,14}))$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Inf__maxLength": "CdtTrfTxInf-RgltryRptg-Dtls-Inf darf höchstens 35 Zeichen lang sein.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Inf__pattern": "CdtTrfTxInf-RgltryRptg-Dtls-Inf muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Amt-Ccy__conditional-required__52538e89+FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Amt-Ccy__required-conditional": "CdtTrfTxInf-RgltryRptg-Dtls-Amt-Ccy ist erforderlich, wenn CdtTrfTxInf-RgltryRptg-Dtls-Amt-amount vorhanden ist.",
    "rule.generated__FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Amt-amount__conditional-required__8b1d02dc+FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Amt-amount__required-conditional": "CdtTrfTxInf-RgltryRptg-Dtls-Amt-amount ist erforderlich, wenn CdtTrfTxInf-RgltryRptg-Dtls-Amt-Ccy vorhanden ist."
  };
