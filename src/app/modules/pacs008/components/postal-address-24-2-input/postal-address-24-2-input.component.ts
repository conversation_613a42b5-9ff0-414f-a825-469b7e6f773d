import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { CBPR_RestrictedFINXMax70Text_ExtendedInputComponent } from '../cbpr-restricted-finx-max-70-text-extended-input';
import { CBPR_RestrictedFINXMax16Text_ExtendedInputComponent } from '../cbpr-restricted-finx-max-16-text-extended-input';
import { CBPR_RestrictedFINXMax35Text_ExtendedInputComponent } from '../cbpr-restricted-finx-max-35-text-extended-input';
import { CountryCodeInputComponent } from '../country-code-input';
import { HiddenSectionComponent, IMBFormArrayInputComponent } from '@shared/ui';

@Component({
  selector: 'app-postal-address-24-2-input',
  imports: [
    CBPR_RestrictedFINXMax70Text_ExtendedInputComponent,
    CBPR_RestrictedFINXMax16Text_ExtendedInputComponent,
    CBPR_RestrictedFINXMax35Text_ExtendedInputComponent,
    CountryCodeInputComponent,
    HiddenSectionComponent,
    IMBFormArrayInputComponent,
  ],
  templateUrl: './postal-address-24-2-input.component.html',
  styleUrl: './postal-address-24-2-input.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PostalAddress24__2InputComponent {
  departmentFieldName = input.required<string>();
  departmentLabel = input.required<string>();
  subDepartmentFieldName = input.required<string>();
  subDepartmentLabel = input.required<string>();
  streetNameFieldName = input.required<string>();
  streetNameLabel = input.required<string>();
  buildingNumberFieldName = input.required<string>();
  buildingNumberLabel = input.required<string>();
  buildingNameFieldName = input.required<string>();
  buildingNameLabel = input.required<string>();
  floorFieldName = input.required<string>();
  floorLabel = input.required<string>();
  postBoxFieldName = input.required<string>();
  postBoxLabel = input.required<string>();
  roomFieldName = input.required<string>();
  roomLabel = input.required<string>();
  postCodeFieldName = input.required<string>();
  postCodeLabel = input.required<string>();
  townNameFieldName = input.required<string>();
  townNameLabel = input.required<string>();
  townLocationNameFieldName = input.required<string>();
  townLocationNameLabel = input.required<string>();
  districtNameFieldName = input.required<string>();
  districtNameLabel = input.required<string>();
  countrySubdivisionFieldName = input.required<string>();
  countrySubdivisionLabel = input.required<string>();
  countryFieldName = input.required<string>();
  countryLabel = input.required<string>();
  addressLineFieldName = input.required<string>();
  addressLineLabel = input.required<string>();

  isReadOnly = input.required<boolean>();
}
