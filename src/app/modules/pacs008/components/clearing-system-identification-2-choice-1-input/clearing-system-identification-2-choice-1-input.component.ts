import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { IMBFormTextInputComponent } from '@shared/ui';

@Component({
  selector: 'app-clearing-system-identification-2-choice-1-input',
  imports: [IMBFormTextInputComponent],
  templateUrl:
    './clearing-system-identification-2-choice-1-input.component.html',
  styleUrl: './clearing-system-identification-2-choice-1-input.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ClearingSystemIdentification2Choice__1InputComponent {
  fieldName = input.required<string>();
  label = input.required<string>();
  isReadOnly = input.required<boolean>();
}
