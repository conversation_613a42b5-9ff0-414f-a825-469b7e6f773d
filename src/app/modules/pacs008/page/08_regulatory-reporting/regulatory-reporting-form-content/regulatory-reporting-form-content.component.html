<app-imb-form-radio-button-group
  [label]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-DbtCdtRptgInd']"
  fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-DbtCdtRptgInd"
  [isReadOnly]="isReadOnly()"
  [options]="regulatoryReportingTypeCodeOptions"
/>
<app-cbpr-restricted-finx-max-140-text-input
  fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Authrty-Nm"
  [label]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Authrty-Nm']"
  [isReadOnly]="isReadOnly()"
/>
<app-country-code-input
  fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Authrty-Ctry"
  [label]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Authrty-Ctry']"
  [isReadOnly]="isReadOnly()"
/>
<app-hidden-section
  header="Details"
  i18n-header
  [errorScopes]="['FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls']"
>
  <app-cbpr-restricted-finx-max-35-text-input
    fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Tp"
    [label]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Tp']"
    [isReadOnly]="isReadOnly()"
  />
  <app-iso-date-input
    [label]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Dt']"
    fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Dt"
    [isReadOnly]="isReadOnly()"
  />
  <app-country-code-input
    fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Ctry"
    [label]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Ctry']"
    [isReadOnly]="isReadOnly()"
  />
  <app-imb-form-text-input
    fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Cd"
    [label]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Cd']"
    [isReadOnly]="isReadOnly()"
  />
  <app-cbpr-amount-1-input
    amountFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Amt-amount"
    [amountLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Amt-amount']
    "
    currencyFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Amt-Ccy"
    [currencyLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Amt-Ccy']
    "
    [isReadOnly]="isReadOnly()"
  />
  <app-cbpr-restricted-finx-max-35-text-input
    fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Inf"
    [label]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Inf']"
    [isReadOnly]="isReadOnly()"
  />
</app-hidden-section>
